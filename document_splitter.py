#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档切分服务调用脚本
功能：读取指定文件夹下的所有文件，调用ZTE IBO ACM文档切分接口，输出切分结果
"""

import os
import json
import requests
from pathlib import Path
from typing import List, Dict, Any
import argparse
import sys
import chardet


class DocumentSplitter:
    """文档切分服务调用类"""
    
    def __init__(self, api_url: str = "https://icosg.test.zte.com.cn/zte-ibo-acm-tools/cut", api_key: str = None):
        """
        初始化文档切分器
        
        Args:
            api_url: 文档切分接口地址
            api_key: API密钥
        """
        self.api_url = api_url
        self.headers = {'Content-Type': 'application/json'}
        
        # 添加API密钥到请求头（如果提供）
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
        elif os.getenv('ZTE_API_KEY'):
            # 从环境变量获取API密钥
            self.headers['Authorization'] = f'Bearer {os.getenv("ZTE_API_KEY")}'
        elif os.getenv('API_KEY'):
            # 通用API密钥环境变量
            self.headers['Authorization'] = f'Bearer {os.getenv("API_KEY")}'
        
        # 支持的文本文件格式
        self.text_extensions = {'.txt', '.md', '.markdown', '.text', '.py', '.js', '.java', 
                              '.cpp', '.c', '.h', '.hpp', '.css', '.html', '.xml', '.json', 
                              '.yaml', '.yml', '.ini', '.cfg', '.conf', '.log'}
    
    def detect_encoding(self, file_path: str) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            编码格式
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                if encoding:
                    return encoding
                else:
                    return 'utf-8'
        except Exception as e:
            print(f"检测文件编码失败 {file_path}: {e}")
            return 'utf-8'
    
    def read_file_content(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容，读取失败时返回空字符串
        """
        try:
            # 首先检测文件编码
            encoding = self.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
                return content
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return ""
    
    def scan_folder(self, folder_path: str) -> List[Dict[str, str]]:
        """
        扫描文件夹，获取所有支持的文本文件
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            文件信息列表，每个元素包含title和content
        """
        file_texts = []
        folder_path = Path(folder_path)
        
        if not folder_path.exists():
            print(f"错误：文件夹不存在: {folder_path}")
            return file_texts
        
        if not folder_path.is_dir():
            print(f"错误：指定路径不是文件夹: {folder_path}")
            return file_texts
        
        print(f"正在扫描文件夹: {folder_path}")
        
        # 递归扫描文件夹
        for file_path in folder_path.rglob('*'):
            if file_path.is_file():
                # 检查文件扩展名
                if file_path.suffix.lower() in self.text_extensions:
                    print(f"正在读取文件: {file_path}")
                    
                    content = self.read_file_content(str(file_path))
                    if content.strip():  # 只处理非空文件
                        file_texts.append({
                            "title": file_path.name,
                            "content": content
                        })
                        print(f"成功读取文件: {file_path.name} (大小: {len(content)} 字符)")
                    else:
                        print(f"跳过空文件: {file_path.name}")
        
        print(f"总共找到 {len(file_texts)} 个有效文件")
        return file_texts
    
    def call_split_api(self, file_texts: List[Dict[str, str]], 
                      headers_level: int = 2,
                      chunk_max_len: int = 2048, 
                      add_filename: bool = False) -> Dict[str, Any]:
        """
        调用文档切分接口
        
        Args:
            file_texts: 文档列表
            headers_level: 切分标题层级，值为1~6，默认为2
            chunk_max_len: 最大切片长度，值为100~2048，默认为2048
            add_filename: 切片中是否添加文件名，默认为False
            
        Returns:
            接口响应结果
        """
        # 构建请求体
        payload = {
            "file_texts": file_texts,
            "headers_level": headers_level,
            "chunk_max_len": chunk_max_len,
            "add_filename": add_filename
        }
        
        print(f"正在调用文档切分接口: {self.api_url}")
        print(f"请求参数: headers_level={headers_level}, chunk_max_len={chunk_max_len}, add_filename={add_filename}")
        print(f"待切分文档数量: {len(file_texts)}")
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=300  # 5分钟超时
            )
            
            # 检查HTTP状态码
            if response.status_code == 200:
                result = response.json()
                print(f"接口调用成功，返回码: {result.get('code', 'N/A')}")
                print(f"返回信息: {result.get('message', 'N/A')}")
                return result
            else:
                print(f"接口调用失败，HTTP状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return {
                    "code": str(response.status_code),
                    "message": f"HTTP错误: {response.status_code}",
                    "bo": []
                }
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return {
                "code": "ERROR",
                "message": f"请求异常: {str(e)}",
                "bo": []
            }
    
    def format_output(self, result: Dict[str, Any], output_file: str = None):
        """
        格式化输出结果
        
        Args:
            result: 接口响应结果
            output_file: 输出文件路径，如果为None则输出到控制台
        """
        output_content = []
        
        # 添加基本信息
        output_content.append("=" * 60)
        output_content.append("文档切分结果")
        output_content.append("=" * 60)
        output_content.append(f"返回码: {result.get('code', 'N/A')}")
        output_content.append(f"返回信息: {result.get('message', 'N/A')}")
        output_content.append("")
        
        # 处理切分结果
        bo_data = result.get('bo', [])
        if bo_data:
            output_content.append(f"切分结果概览:")
            output_content.append(f"- 处理文档数量: {len(bo_data)}")
            
            total_chunks = sum(len(chunks) for chunks in bo_data)
            output_content.append(f"- 生成切片总数: {total_chunks}")
            output_content.append("")
            
            # 详细切分结果
            output_content.append("详细切分结果:")
            output_content.append("-" * 40)
            
            for i, chunks in enumerate(bo_data):
                output_content.append(f"\n文档 {i+1} 的切分结果 (共 {len(chunks)} 个切片):")
                output_content.append("=" * 50)
                
                for j, chunk in enumerate(chunks):
                    output_content.append(f"\n切片 {j+1}:")
                    output_content.append("-" * 20)
                    # 限制每个切片的显示长度，避免输出过长
                    chunk_preview = chunk[:500] + "..." if len(chunk) > 500 else chunk
                    output_content.append(chunk_preview)
                    output_content.append(f"(切片长度: {len(chunk)} 字符)")
        else:
            output_content.append("未获取到切分结果")
        
        # 输出结果
        output_text = "\n".join(output_content)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(output_text)
                print(f"\n结果已保存到文件: {output_file}")
            except Exception as e:
                print(f"保存文件失败: {e}")
                print("\n结果输出到控制台:")
                print(output_text)
        else:
            print("\n" + output_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='文档切分服务调用脚本')
    
    parser.add_argument('folder_path', 
                       help='要处理的文件夹路径')
    
    parser.add_argument('--api-url', 
                       default='https://icosg.test.zte.com.cn/zte-ibo-acm-tools/cut',
                       help='文档切分接口地址')
    
    parser.add_argument('--api-key', 
                       help='API密钥 (也可通过环境变量ZTE_API_KEY或API_KEY设置)')
    
    parser.add_argument('--headers-level', 
                       type=int, 
                       default=2, 
                       choices=range(1, 7),
                       help='切分标题层级 (1-6, 默认: 2)')
    
    parser.add_argument('--chunk-max-len', 
                       type=int, 
                       default=2048, 
                       help='最大切片长度 (100-2048, 默认: 2048)')
    
    parser.add_argument('--add-filename', 
                       action='store_true',
                       help='在切片中添加文件名')
    
    parser.add_argument('--output', 
                       help='输出文件路径 (可选，默认输出到控制台)')
    
    args = parser.parse_args()
    
    # 参数验证
    if not (100 <= args.chunk_max_len <= 2048):
        print("错误: chunk_max_len 必须在 100-2048 之间")
        sys.exit(1)
    
    # 创建文档切分器
    splitter = DocumentSplitter(args.api_url, args.api_key)
    
    # 扫描文件夹
    file_texts = splitter.scan_folder(args.folder_path)
    
    if not file_texts:
        print("未找到任何可处理的文件")
        sys.exit(1)
    
    # 调用接口
    result = splitter.call_split_api(
        file_texts=file_texts,
        headers_level=args.headers_level,
        chunk_max_len=args.chunk_max_len,
        add_filename=args.add_filename
    )
    
    # 格式化输出结果
    splitter.format_output(result, args.output)


if __name__ == "__main__":
    main()
