"""
文档解析工具
用于将文件转换为Markdown格式的API客户端
"""

import requests
import time
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Optional, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from utils.http_util import post_no_proxy
# 确保代理配置生效
try:
    from proxy_config import configure_proxy_bypass
    configure_proxy_bypass()
except ImportError:
    pass  # 如果没有代理配置文件，忽略错误


class DocumentParser:
    """文档解析器类"""
    
    def __init__(self, app_code: str = "d25d52877fde483bbae65fbc5465375f"):
        self.app_code = app_code
        self.base_url = "https://icosg.dt.zte.com.cn/zte-igpt-documentparsing"
        self.headers = {"appcode": app_code}
        # 默认不走代理
        self.proxies = {"http": None, "https": None}
    
    def parse_file(
        self, 
        file_path: str, 
        get_image: bool = True,
        use_llm_pdf: bool = False,
        dewater: bool = False,
        dewater_type: str = 'speed',
        poll_interval: int = 2
    ) -> str:
        """
        解析文件并返回Markdown内容
        
        Args:
            file_path: 文件路径
            get_image: 是否获取图片
            use_llm_pdf: 是否使用LLM处理PDF
            dewater: 是否去水印
            dewater_type: 去水印类型
            poll_interval: 轮询间隔（秒）
            
        Returns:
            解析后的Markdown内容
            
        Raises:
            FileNotFoundError: 文件不存在
            Exception: API调用失败或解析错误
        """
        # 验证文件存在
        if not Path(file_path).exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 发送解析请求
        task_id = self._send_parse_request(
            file_path, get_image, use_llm_pdf, dewater, dewater_type
        )
        
        # 轮询获取结果
        return self._poll_for_result(task_id, poll_interval)
    
    def _send_parse_request(
        self, 
        file_path: str, 
        get_image: bool,
        use_llm_pdf: bool,
        dewater: bool,
        dewater_type: str
    ) -> str:
        """发送文件解析请求"""
        url = f"{self.base_url}/file_to_markdown"
        
        data = {
            'use_llm_pdf': use_llm_pdf,
            'dewater': dewater,
            'dewater_type': dewater_type,
            'get_image': get_image
        }
        
        try:
            with open(file_path, 'rb') as file:
                files = {'file': file}
                response = post_no_proxy(url, files=files, headers=self.headers, data=data)
                response.raise_for_status()
                
                result = response.json()
                return result['bo']['task_id']
                
        except requests.RequestException as e:
            raise Exception(f"发送解析请求失败: {e}")
        except KeyError:
            raise Exception("响应格式错误，未找到task_id")
    
    def _poll_for_result(self, task_id: str, poll_interval: int) -> str:
        """轮询获取解析结果"""
        while True:
            response_data = self._get_task_status(task_id)
            status = response_data['bo'][task_id]['status']
            
            if status == 'completed':
                return response_data['bo'][task_id]['content']
            elif status == 'error':
                error_info = response_data['bo'][task_id].get('error_info', '未知错误')
                raise Exception(f"文档解析失败: {error_info}")
            else:
                time.sleep(poll_interval)
    
    def _get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        url = f"{self.base_url}/task-status"
        data = {'task_id': task_id}
        
        try:
            response = post_no_proxy(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"获取任务状态失败: {e}")



def file_parse(file_path: str, get_image: bool = True) -> str:
    """
    解析文件的便捷函数
    
    Args:
        file_path: 文件路径
        get_image: 是否获取图片
        
    Returns:
        解析后的Markdown内容
    """
    parser = DocumentParser()
    return parser.parse_file(file_path, get_image=get_image)


def save_content_to_txt(content: str, output_path: str = None) -> str:
    """
    保存解析内容到txt文件
    
    Args:
        content: 解析后的内容
        output_path: 输出文件路径，如果不提供则自动生成
        
    Returns:
        保存的文件路径
    """
    if output_path is None:
        # 自动生成文件名，使用时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"parsed_document_{timestamp}.txt"
    
    # 确保输出目录存在
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存内容到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return str(output_file.absolute())


def get_supported_files(folder_path: str) -> List[Dict[str, str]]:
    """
    扫描文件夹中支持的文档文件
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        包含文件信息的字典列表，每个字典包含path、type、output_path
    """
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise ValueError(f"文件夹不存在或不是有效目录: {folder_path}")
    
    # 支持的文件扩展名及其类型
    supported_extensions = {
        '.pdf': 'PDF',
        '.docx': 'DOCX',
        '.doc': 'DOC'
    }
    
    supported_files = []
    
    # 遍历文件夹中的所有文件
    for file_path in folder.iterdir():
        if file_path.is_file():
            file_ext = file_path.suffix.lower()
            if file_ext in supported_extensions:
                # 生成输出文件名
                output_name = f"{file_path.stem}.txt"
                output_path = folder / "parsed_output" / output_name
                
                supported_files.append({
                    "path": str(file_path),
                    "type": supported_extensions[file_ext],
                    "output_path": str(output_path)
                })
    
    return supported_files


# 线程安全的打印锁
_print_lock = threading.Lock()

def _thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with _print_lock:
        print(*args, **kwargs)


def _process_single_file(file_info: Dict[str, str], parser: DocumentParser, get_image: bool, file_index: int, total_files: int) -> Dict[str, Any]:
    """
    处理单个文件的线程函数
    
    Args:
        file_info: 文件信息字典
        parser: 文档解析器实例
        get_image: 是否获取图片
        file_index: 文件索引
        total_files: 总文件数
        
    Returns:
        处理结果字典
    """
    file_path = Path(file_info["path"])
    file_type = file_info["type"]
    output_path = file_info["output_path"]
    
    thread_id = threading.current_thread().name
    _thread_safe_print(f"\n[{file_index}/{total_files}] [线程:{thread_id}] 正在处理: {file_path.name} ({file_type})")
    
    try:
        # 根据文件类型选择解析参数
        if file_type == "PDF":
            # PDF文件使用LLM模式，不获取图片以提高速度
            content = parser.parse_file(str(file_path), get_image=False, use_llm_pdf=True)
        else:
            # DOC/DOCX文件使用标准参数
            content = parser.parse_file(str(file_path), get_image=get_image)
        
        # 保存解析结果
        saved_path = save_content_to_txt(content, output_path)
        
        result = {
            "source": str(file_path),
            "output": saved_path,
            "type": file_type,
            "size": len(content),
            "status": "success",
            "preview": content[:150].strip() + "..." if len(content) > 150 else content,
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ✓ [{thread_id}] 解析成功! 内容长度: {len(content):,} 字符")
        _thread_safe_print(f"  📁 [{thread_id}] 输出文件: {Path(saved_path).name}")
        _thread_safe_print(f"  📝 [{thread_id}] 内容预览: {result['preview']}")
        
        return result
        
    except Exception as e:
        error_result = {
            "source": str(file_path),
            "output": None,
            "type": file_type,
            "size": 0,
            "status": "error",
            "error": str(e),
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ✗ [{thread_id}] 解析失败: {e}")
        return error_result


def parse_folder(folder_path: str, get_image: bool = True, create_output_dir: bool = True, max_workers: int = 10) -> List[Dict[str, Any]]:
    """
    并行解析文件夹中的所有支持的文档
    
    Args:
        folder_path: 文件夹路径
        get_image: 是否获取图片
        create_output_dir: 是否创建输出目录
        max_workers: 最大线程数（默认10个）
        
    Returns:
        处理结果列表，包含成功和失败的文件信息
    """
    folder = Path(folder_path)
    
    # 获取支持的文件列表
    supported_files = get_supported_files(folder_path)
    
    if not supported_files:
        print(f"在文件夹 {folder_path} 中未找到支持的文档文件")
        print("支持的文件类型：PDF (.pdf), Word文档 (.docx, .doc)")
        return []
    
    # 创建输出目录
    if create_output_dir:
        output_dir = folder / "parsed_output"
        output_dir.mkdir(exist_ok=True)
        print(f"输出目录: {output_dir}")
    
    print(f"=== 开始并行解析文件夹: {folder_path} ===")
    print(f"找到 {len(supported_files)} 个支持的文件")
    print(f"使用 {max_workers} 个线程并行处理")
    
    # 为每个线程创建独立的解析器实例
    results = []
    completed_count = 0
    
    # 使用线程池并行处理文件
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="DocParser") as executor:
        # 提交所有任务
        future_to_file = {}
        for i, file_info in enumerate(supported_files, 1):
            # 每个线程使用独立的解析器实例
            parser = DocumentParser()
            future = executor.submit(_process_single_file, file_info, parser, get_image, i, len(supported_files))
            future_to_file[future] = file_info
        
        print(f"\n所有任务已提交，开始并行处理...")
        
        # 收集结果
        for future in as_completed(future_to_file):
            try:
                result = future.result()
                results.append(result)
                completed_count += 1
                
                # 显示整体进度
                progress = completed_count / len(supported_files) * 100
                _thread_safe_print(f"\n>>> 整体进度: {completed_count}/{len(supported_files)} ({progress:.1f}%) 已完成 <<<")
                
            except Exception as e:
                file_info = future_to_file[future]
                error_result = {
                    "source": file_info["path"],
                    "output": None,
                    "type": file_info["type"],
                    "size": 0,
                    "status": "error",
                    "error": f"线程异常: {str(e)}",
                    "thread_id": "unknown"
                }
                results.append(error_result)
                completed_count += 1
                _thread_safe_print(f"✗ 处理文件时发生异常: {Path(file_info['path']).name} - {e}")
    
    # 按原始文件顺序排序结果
    file_path_order = {file_info["path"]: i for i, file_info in enumerate(supported_files)}
    results.sort(key=lambda x: file_path_order.get(x["source"], float('inf')))
    
    # 输出处理摘要
    success_count = len([r for r in results if r["status"] == "success"])
    error_count = len([r for r in results if r["status"] == "error"])
    
    print(f"\n=== 并行处理完成 ===")
    print(f"总计: {len(results)} 个文件")
    print(f"成功: {success_count} 个, 失败: {error_count} 个")
    print(f"使用线程数: {max_workers}")
    
    if success_count > 0:
        print(f"\n✅ 成功解析的文件:")
        for result in results:
            if result["status"] == "success":
                thread_info = f"[{result.get('thread_id', 'unknown')}]"
                print(f"  • {result['type']}: {Path(result['source']).name} → {Path(result['output']).name} {thread_info}")
                print(f"    内容长度: {result['size']:,} 字符")
    
    if error_count > 0:
        print(f"\n❌ 解析失败的文件:")
        for result in results:
            if result["status"] == "error":
                thread_info = f"[{result.get('thread_id', 'unknown')}]"
                print(f"  • {result['type']}: {Path(result['source']).name} - {result['error']} {thread_info}")
    
    return results


if __name__ == "__main__":
    # ==================== 配置参数 ====================
    # 在这里直接定义来源文件夹路径
    来源文件夹路径 = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document"  # 修改此路径为你的目标文件夹
    
    # 其他可选参数
    获取图片 = False        # 是否获取图片（Word文档）
    创建输出目录 = True     # 是否自动创建输出目录
    并行线程数 = 10         # 并行处理的线程数量
    
    # ==================================================
    
    print("=== 批量文档解析工具 ===")
    print(f"来源文件夹: {来源文件夹路径}")
    print(f"图片获取: {'启用' if 获取图片 else '禁用'}")
    print(f"输出目录: {'自动创建' if 创建输出目录 else '使用现有'}")
    print(f"并行线程数: {并行线程数} 个")
    
    try:
        # 使用新的并行文件夹解析功能
        results = parse_folder(
            folder_path=来源文件夹路径, 
            get_image=获取图片,
            create_output_dir=创建输出目录,
            max_workers=并行线程数
        )
        
        if not results:
            print(f"\n在文件夹 '{来源文件夹路径}' 中未找到支持的文档文件")
            print("\n使用说明:")
            print("1. 修改代码中的 '来源文件夹路径' 变量")
            print("2. 调整 '并行线程数' 参数（当前设置：10个线程）")
            print("3. 将PDF、DOCX或DOC文件放入指定文件夹")
            print("4. 运行: python file_parse.py")
            print("5. 解析结果将保存在 parsed_output 子文件夹中")
            print(f"\n当前设置:")
            print(f"  文件夹路径: {来源文件夹路径}")
            print(f"  并行线程数: {并行线程数} 个")
            print("  支持的文件类型: PDF (.pdf), Word文档 (.docx, .doc)")
    
    except Exception as e:
        print(f"错误: {e}")
        print(f"\n请检查文件夹路径: {来源文件夹路径}")
        print("确认:")
        print("1. 文件夹路径是否正确")
        print("2. 是否有访问权限")
        print("3. 文件夹中是否包含支持的文档文件")