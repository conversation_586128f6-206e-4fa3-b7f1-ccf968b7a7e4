# 文本切分处理结果

## 处理信息
- 输入文件夹: D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\doc\docx_parse\output
- 输出文件夹: D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\doc\md_text_split\md_text_split\output
- 处理时间: 2025-09-02 20:34:28

## 统计信息
- 处理文件数: 1
- 生成chunk数: 68
- 标题层级: 6
- 最大块长度: 2047

## 支持的文件格式
.md, .txt, .markdown

## 文件命名规则
chunk文件命名格式: `chunk_{原文件名}_{序号}.txt`

## 详细日志
请查看 `processing_report.json` 文件获取详细的处理日志。
