# Chunk对比工具使用说明

## 📋 功能概述
`chunk_comparator.py` 是一个专门用于对比两个chunk脚本（API版本和本地版本）对同一文件处理结果的工具。

## 🎯 主要功能

### 1. 单文件对比
- 对比两个JSON文件的chunk切分结果
- 分析内容相似度、chunk数量、大小分布等
- 生成详细的对比报告和质量评分

### 2. 批量文件夹对比
- 批量对比两个文件夹中的所有chunk文件
- 生成统计汇总和趋势分析
- 输出整体质量评估报告

## 🔧 使用方法

### 单文件对比
```bash
python chunk_comparator.py compare <api_output.json> <local_output.json>
```

**示例**:
```bash
python chunk_comparator.py compare ./api_results/document1_chunks.json ./local_results/document1_chunks.json
```

### 批量文件夹对比
```bash
python chunk_comparator.py batch <api_folder> <local_folder> [report_file]
```

**示例**:
```bash
python chunk_comparator.py batch ./api_results ./local_results batch_report.txt
```

## 📊 对比指标详解

### 1. 基础统计指标
- **Chunk数量对比**: 两种方法生成的chunk数量差异
- **内容大小分析**: 总内容长度、平均chunk大小等
- **数量比率**: chunk数量的相对关系

### 2. 相似度指标
- **字符级相似度**: 基于字符序列的相似度计算
- **行级相似度**: 基于行结构的相似度分析
- **词级相似度**: 基于词汇分布的相似度评估
- **平均相似度**: 上述三种相似度的综合平均

### 3. 内容覆盖分析
- **覆盖率**: 第一组chunk在第二组中找到高相似匹配的比例
- **高相似度匹配**: 相似度超过0.8的匹配数量
- **最佳匹配分析**: 每个chunk的最佳对应关系

### 4. 质量评分
- **内容覆盖分数**: 0-1之间，评估内容覆盖完整性
- **相似度分数**: 0-1之间，评估整体内容相似度
- **大小一致性**: 0-1之间，评估chunk大小分布的一致性
- **数量一致性**: 0-1之间，评估chunk数量的一致性

## 📈 评分标准

### 质量等级划分
- **优秀 (Excellent)**: 得分 > 0.9
- **良好 (Good)**: 得分 0.7 - 0.9
- **一般 (Fair)**: 得分 0.5 - 0.7
- **较差 (Poor)**: 得分 < 0.5

### 综合评价算法
```
综合得分 = (内容覆盖分数 + 相似度分数 + 大小一致性 + 数量一致性) / 4
```

## 📋 报告内容

### 单文件对比报告包含
1. **文件基本信息**: 文件路径、处理方法、源文件匹配状态
2. **数量统计**: chunk数量对比和差异分析
3. **大小分析**: 内容总长度、平均chunk大小等
4. **相似度分析**: 多维度相似度指标
5. **质量评分**: 四个维度的质量分数
6. **详细对比**: 前10个chunk的逐一对比
7. **综合评价**: 总体质量评估和改进建议

### 批量对比报告包含
1. **总体统计**: 对比文件数量、处理成功率
2. **质量分布**: 各质量等级的文件分布
3. **平均指标**: 所有指标的平均值
4. **详细列表**: 每个文件的得分情况
5. **趋势分析**: 整体一致性评估

## 🎯 使用场景

### 1. 算法验证
- 验证API版本和本地版本的切分结果一致性
- 识别两种方法的差异和优劣

### 2. 质量评估
- 评估切分质量和稳定性
- 发现可能的问题文件

### 3. 参数调优
- 对比不同参数设置的效果
- 优化切分策略

### 4. 版本对比
- 对比不同版本的处理效果
- 验证更新后的兼容性

## 💡 使用建议

### 1. 批量对比工作流
```bash
# 1. 使用API版本处理文档
python split_api_test.py

# 2. 使用本地版本处理相同文档  
python text_split.py

# 3. 批量对比结果
python chunk_comparator.py batch ./api_output ./local_output comparison_report.txt

# 4. 分析报告，关注质量较差的文件
```

### 2. 重点关注指标
- **内容覆盖分数 < 0.7**: 可能存在内容丢失或切分逻辑差异
- **相似度分数 < 0.7**: 内容表述可能有显著差异
- **数量一致性 < 0.5**: 切分粒度差异较大
- **大小一致性 < 0.5**: chunk大小分布差异明显

### 3. 问题诊断流程
1. **查看综合得分**: 快速了解整体一致性
2. **分析具体指标**: 定位问题维度
3. **检查详细对比**: 了解具体差异点
4. **参考改进建议**: 获得优化方向

## ⚠️ 注意事项

### 1. 文件格式要求
- 两个文件必须是同一源文件的不同处理结果
- JSON格式必须符合统一的chunk格式规范
- 文件编码必须为UTF-8

### 2. 性能考虑
- 大文件对比可能需要较长时间
- 批量对比时建议合理设置并发数
- 详细对比限制前10个chunk以提高性能

### 3. 结果解读
- 相似度不是越高越好，要结合实际需求
- 某些差异可能是算法特性，不一定是问题
- 建议结合人工检查验证重要结果

## 📊 输出文件

### 自动生成的文件
- `comparison_result_YYYYMMDD_HHMMSS.json`: 详细对比数据
- `batch_comparison_YYYYMMDD_HHMMSS.txt`: 批量对比报告
- 用户指定的报告文件

### 文件用途
- JSON文件: 用于进一步分析或程序处理
- TXT文件: 用于人工阅读和报告展示

## 🚀 扩展功能

该工具支持进一步扩展：
- 自定义相似度算法
- 添加更多统计指标
- 集成可视化图表
- 支持其他格式的chunk文件
