#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接运行对比脚本

一键对比您的两个chunk文件夹
"""

import os
import sys
from datetime import datetime
from chunk_comparator import ChunkComparator

def main():
    """主对比函数"""
    
    # 🔧 在这里修改您的文件夹路径
    api_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_api_chunks_20250907_170250"
    local_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_json_chunks_20250907_170302"
    
    print("🎯 Chunk文件夹对比工具")
    print("=" * 80)
    print(f"📁 API版本输出文件夹:")
    print(f"   {api_folder}")
    print(f"📁 本地版本输出文件夹:")
    print(f"   {local_folder}")
    print("=" * 80)
    
    # 检查文件夹是否存在
    if not os.path.exists(api_folder):
        print(f"❌ 错误: API版本文件夹不存在")
        print(f"   请检查路径: {api_folder}")
        print("💡 请修改脚本中的 api_folder 路径")
        return
    
    if not os.path.exists(local_folder):
        print(f"❌ 错误: 本地版本文件夹不存在")
        print(f"   请检查路径: {local_folder}")
        print("💡 请修改脚本中的 local_folder 路径")
        return
    
    # 快速统计文件数量
    api_files = [f for f in os.listdir(api_folder) if f.endswith('_chunks.json')]
    local_files = [f for f in os.listdir(local_folder) if f.endswith('_chunks.json')]
    
    print(f"📊 文件统计:")
    print(f"   API版本文件: {len(api_files)} 个")
    print(f"   本地版本文件: {len(local_files)} 个")
    
    if len(api_files) == 0 or len(local_files) == 0:
        print("❌ 错误: 找不到chunk文件 (*_chunks.json)")
        print("💡 请确认文件夹中包含正确的chunk文件")
        return
    
    # 生成报告文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"chunk_comparison_report_{timestamp}.txt"
    json_file = f"chunk_comparison_data_{timestamp}.json"
    
    print(f"\n📄 将生成报告:")
    print(f"   文本报告: {report_file}")
    print(f"   详细数据: {json_file}")
    print()
    
    # 开始对比
    print("🚀 开始对比...")
    
    try:
        comparator = ChunkComparator()
        result = comparator.batch_compare_folders(api_folder, local_folder, report_file)
        
        if "error" in result:
            print(f"\n❌ 对比失败: {result['error']}")
            if "details" in result:
                details = result["details"]
                print(f"\n📋 详细信息:")
                print(f"   API文件夹文件数: {details.get('folder1_files', 0)}")
                print(f"   本地文件夹文件数: {details.get('folder2_files', 0)}")
                
                if details.get('only_in_folder1'):
                    print(f"   仅在API文件夹: {len(details['only_in_folder1'])} 个")
                if details.get('only_in_folder2'):
                    print(f"   仅在本地文件夹: {len(details['only_in_folder2'])} 个")
            return
        
        # 保存详细数据
        import json
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 显示结果摘要
        stats = result["summary_stats"]
        
        print(f"\n✅ 对比完成!")
        print("=" * 60)
        print(f"📊 对比摘要:")
        print(f"   成功对比文件数: {stats['total_comparisons']}")
        print()
        print(f"📈 质量分布:")
        print(f"   🎉 优秀 (>0.9): {stats['excellent_count']:3d} 个 ({stats['excellent_count']/stats['total_comparisons']*100:5.1f}%)")
        print(f"   👍 良好 (0.7-0.9): {stats['good_count']:3d} 个 ({stats['good_count']/stats['total_comparisons']*100:5.1f}%)")
        print(f"   ⚠️ 一般 (0.5-0.7): {stats['fair_count']:3d} 个 ({stats['fair_count']/stats['total_comparisons']*100:5.1f}%)")
        print(f"   ❌ 较差 (<0.5): {stats['poor_count']:3d} 个 ({stats['poor_count']/stats['total_comparisons']*100:5.1f}%)")
        print()
        
        print(f"📏 平均指标:")
        print(f"   内容覆盖分数: {stats['avg_coverage_score']:.3f}")
        print(f"   相似度分数:   {stats['avg_similarity_score']:.3f}")
        print(f"   大小一致性:   {stats['avg_size_consistency']:.3f}")
        print(f"   数量一致性:   {stats['avg_chunk_count_consistency']:.3f}")
        
        # 计算综合得分
        overall_score = (stats['avg_coverage_score'] + stats['avg_similarity_score'] + 
                        stats['avg_size_consistency'] + stats['avg_chunk_count_consistency']) / 4
        
        print(f"\n🎯 综合评价:")
        print(f"   总体得分: {overall_score:.3f}")
        
        if overall_score > 0.9:
            print("   评价: 🎉 两种方法的切分结果一致性优秀！")
            print("   建议: ✅ 两种方法可以互相验证，结果可信度高")
        elif overall_score > 0.7:
            print("   评价: 👍 两种方法的切分结果一致性良好")
            print("   建议: ✅ 整体效果不错，可关注个别差异较大的文件")
        elif overall_score > 0.5:
            print("   评价: ⚠️ 两种方法的切分结果存在一定差异")
            print("   建议: 🔍 建议检查参数设置和处理逻辑")
        else:
            print("   评价: ❌ 两种方法的切分结果差异较大")
            print("   建议: 🛠️ 需要深入分析差异原因，调整处理策略")
        
        print("\n📋 问题文件 (得分<0.7):")
        problem_count = 0
        for result_item in result["detailed_results"]:
            if "overall_score" in result_item and result_item["overall_score"] < 0.7:
                problem_count += 1
                score = result_item["overall_score"]
                name = result_item.get("base_name", "unknown")
                print(f"   {problem_count:2d}. {name} (得分: {score:.3f})")
                if problem_count >= 10:  # 只显示前10个
                    remaining = sum(1 for r in result["detailed_results"] 
                                  if "overall_score" in r and r["overall_score"] < 0.7) - 10
                    if remaining > 0:
                        print(f"       ... 还有 {remaining} 个问题文件")
                    break
        
        if problem_count == 0:
            print("   🎉 没有发现问题文件，所有文件质量都很好！")
        
        print("\n📄 详细报告:")
        print(f"   📋 文本报告: {report_file}")
        print(f"   📊 详细数据: {json_file}")
        print()
        print("💡 使用建议:")
        print("   1. 查看文本报告了解详细分析")
        print("   2. 关注得分较低的文件，进行人工检查")
        print("   3. 如有问题，可调整切分参数重新处理")
        
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        print("💡 请检查:")
        print("   1. 文件夹路径是否正确")
        print("   2. 文件夹中是否包含有效的chunk文件")
        print("   3. 文件是否有读取权限")

if __name__ == "__main__":
    main()
