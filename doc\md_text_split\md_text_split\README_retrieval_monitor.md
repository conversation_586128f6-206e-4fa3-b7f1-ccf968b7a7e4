# 三路检索监控分析器使用指南

## 概述

三路检索监控分析器是一个专门用于分析ES、Mi<PERSON><PERSON><PERSON>、KG三路检索效果的监控工具。它能够：

- 🔍 跟踪每路检索结果的序号和排序
- 📊 分析最终重排序后的贡献度和占比
- 📈 评估各路检索的有效性得分
- 📋 生成详细的分析报告
- 📊 创建可视化图表

## 核心功能

### 1. 检索结果跟踪
- 为每路检索的每个结果分配序号
- 跟踪原始排序位置
- 记录最终重排序后的位置

### 2. 效果分析指标
- **贡献率**: 各路在最终结果中的占比
- **前10名数量**: 排在前10名的结果数量
- **前20名数量**: 排在前20名的结果数量
- **平均排名**: 该路检索结果的平均排名位置
- **有效性得分**: 综合质量和数量的评估分数

### 3. 有效性得分计算公式
```
质量得分 = (前10名数量 × 2 + 前20名数量) / 总数量
有效性得分 = 质量得分 × 贡献率
```

## 快速开始

### 基本使用示例

```python
from retrieval_monitor import RetrievalMonitor

# 1. 创建监控器
monitor = RetrievalMonitor(
    query_id="Q000001",
    query_text="查询内容"
)

# 2. 添加各路检索结果
# ES检索结果
es_results = [
    {"content": "ES结果1内容", "score": 0.95},
    {"content": "ES结果2内容", "score": 0.90},
    # ...
]
monitor.add_es_results(es_results)

# Milvus检索结果
milvus_results = [
    {"content": "Milvus结果1内容", "score": 0.88},
    {"content": "Milvus结果2内容", "score": 0.85},
    # ...
]
monitor.add_milvus_results(milvus_results)

# KG检索结果
kg_results = [
    {"content": "KG结果1内容", "score": 0.92},
    # ...
]
monitor.add_kg_results(kg_results)

# 3. 设置最终重排序结果
final_results = [
    {"content": "最终结果1", "score": 0.96},
    {"content": "最终结果2", "score": 0.94},
    # ...
]
monitor.set_final_results(final_results)

# 4. 生成分析报告
monitor.print_analysis_report()

# 5. 保存报告和图表
report_file = monitor.save_report_to_file()
chart_file = monitor.create_visualization()
```

### 从监控日志解析

```python
from retrieval_monitor import parse_monitor_log

# 解析监控日志
log_text = """
2025-08-28 20:35:19 INFO: [监控] FAQ查询已记录 - ID: Q000038, 查询: M2000-8S支持哪些接口..., 响应时间: 3.229s
2025-08-28 20:35:19 INFO: [监控] ES: 20条, Milvus: 20条, KG: 1条, 重排序: 36条
"""

log_data = parse_monitor_log(log_text)
print(f"ES数量: {log_data['es_count']}")
print(f"Milvus数量: {log_data['milvus_count']}")
print(f"KG数量: {log_data['kg_count']}")
```

## 分析报告解读

### 报告结构
```json
{
  "query_info": {
    "query_id": "查询ID",
    "query_text": "查询内容",
    "response_time": "响应时间",
    "timestamp": "分析时间"
  },
  "retrieval_counts": {
    "ES": "ES检索数量",
    "Milvus": "Milvus检索数量", 
    "KG": "KG检索数量",
    "final_total": "最终结果总数"
  },
  "source_analysis": {
    "ES": {
      "总数量": 20,
      "前10名数量": 5,
      "前20名数量": 11,
      "平均排名": 16.3,
      "贡献率": "48.78%",
      "有效性得分": 0.5122
    }
    // Milvus, KG 同样结构
  },
  "ranking_details": [
    {
      "最终排名": 1,
      "来源": "KG",
      "原始序号": 1,
      "得分": 0.95,
      "内容预览": "内容预览..."
    }
    // 每个最终结果的详细信息
  ],
  "effectiveness_ranking": [
    {
      "排名": 1,
      "检索路径": "ES",
      "有效性得分": 0.5122,
      "评级": "优秀"
    }
    // 按有效性得分排序的路径排行
  ]
}
```

### 评级标准
- **优秀**: 有效性得分 ≥ 0.3
- **良好**: 有效性得分 ≥ 0.2
- **一般**: 有效性得分 ≥ 0.1
- **较差**: 有效性得分 < 0.1

## 实际示例分析

基于提供的监控日志：
```
ES: 20条, Milvus: 20条, KG: 1条, 重排序: 36条
```

### 分析结果解读

1. **ES检索路径** 🥇
   - 贡献率: 48.78% (20/41)
   - 前10名: 5个，前20名: 11个
   - 平均排名: 16.3
   - 有效性得分: 0.5122 (优秀)
   - **结论**: ES在数量和质量上都表现最佳

2. **Milvus检索路径** 🥈
   - 贡献率: 48.78% (20/41)
   - 前10名: 4个，前20名: 8个
   - 平均排名: 26.7
   - 有效性得分: 0.3902 (优秀)
   - **结论**: 数量与ES相当，但排名质量略低

3. **KG检索路径** 🥉
   - 贡献率: 2.44% (1/41)
   - 前10名: 1个，前20名: 1个
   - 平均排名: 1.0
   - 有效性得分: 0.0732 (较差)
   - **结论**: 虽然单个结果质量很高(排名第1)，但数量太少

### 优化建议

1. **ES路径**: 继续保持，可考虑进一步优化检索算法
2. **Milvus路径**: 需要提升检索结果的相关性和排序质量
3. **KG路径**: 需要扩大知识图谱覆盖范围，增加检索结果数量

## 可视化图表说明

生成的图表包含4个子图：

1. **贡献率饼图**: 显示各路检索在最终结果中的占比
2. **有效性得分对比**: 柱状图比较各路的有效性得分
3. **排名分布热力图**: 显示各路在不同排名段的分布情况
4. **综合评估雷达图**: 多维度对比各路检索的表现

## 高级功能

### 自定义评估指标

```python
# 可以通过继承RetrievalMonitor类来自定义评估逻辑
class CustomRetrievalMonitor(RetrievalMonitor):
    def _calculate_custom_score(self, source_items):
        # 自定义评分逻辑
        pass
```

### 批量分析

```python
# 批量分析多个查询的检索效果
monitors = []
for query_data in query_list:
    monitor = RetrievalMonitor(query_data['id'], query_data['text'])
    # 添加检索结果...
    monitors.append(monitor)

# 生成汇总报告
summary_report = generate_summary_report(monitors)
```

## 注意事项

1. **内容匹配**: 系统通过内容的前100个字符来匹配原始检索结果和最终结果
2. **序号从1开始**: 所有序号都从1开始计数
3. **图表依赖**: 可视化功能需要matplotlib库
4. **内存使用**: 大量检索结果可能占用较多内存

## 故障排除

### 常见问题

1. **内容匹配失败**: 确保最终结果的内容与原始检索结果内容一致
2. **图表生成失败**: 检查matplotlib库是否正确安装
3. **中文显示问题**: 确保系统已安装中文字体

### 调试技巧

```python
# 开启详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查匹配情况
monitor = RetrievalMonitor()
# ... 添加数据后
print(f"最终结果数量: {len(monitor.final_results)}")
print(f"未匹配结果: {len([r for r in monitor.final_results if r.source == 'Unknown'])}")
```

## 总结

三路检索监控分析器提供了全面的检索效果评估能力，通过多维度的指标和可视化展示，帮助您：

- ✅ 客观评估各路检索的效果
- ✅ 发现检索系统的优化点
- ✅ 为检索策略调整提供数据支持
- ✅ 持续监控和改进检索质量

定期使用此工具分析检索效果，可以显著提升检索系统的整体性能。

