<!-- Generated by SpecStory -->

# Add main function for file processing (2025-09-02 12:17Z)

_**User**_

@md_text_split/ @text_split.py 加一个main函数，入参是文件夹，出参是处理后的 响应文件夹和其中的chunk——xxx.txt

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户需要我在`text_split.py`文件中添加一个main函数，功能是：
1. 输入参数：文件夹路径
2. 输出：处理后的响应文件夹和其中的chunk—xxx.txt文件

看起来用户想要一个能够批量处理文件夹中所有Markdown文件的主函数，将每个文件切分成多个chunk，并保存为单独的txt文件。

我需要分析现有的代码结构：
1. `MarkdownSplitter`类是主要的切分器
2. `file_split`函数是现有的主函数，可以处理单个文件
3. 需要添加一个新的main函数来处理整个文件夹

设计思路：
1. 遍历指定文件夹中的所有文件（可能需要递归）
2. 对每个文件使用现有的`file_split`函数进行处理
3. 将每个文件的chunk保存为独立的txt文件
4. 创建输出文件夹结构

我需要考虑：
- 文件过滤（只处理支持的格式，如.md, .txt等）
- 输出文件夹结构
- chunk文件的命名规则
- 错误处理

让我创建这个main函数。</details></think>

---

我将为您在`text_split.py`文件中添加一个main函数，用于批量处理文件夹中的文件并输出chunk文件。

---

<details><summary>Edit file: doc/md_text_split/md_text_split/text_split.py</summary>

undefined

**Chunk 1**
Lines added: 193, lines removed: 1

```diff
@@ -826,5 +826,197 @@
  
  
- 
+ def main(input_folder: str, output_folder: str = None, headers_level: int = 6, 
+          max_chunk_len: int = 800, supported_extensions: List[str] = None) -> str:
+     """
+     批量处理文件夹中的文件，将每个文件切分为多个chunk文件
+     
+     Args:
+         input_folder: 输入文件夹路径
+         output_folder: 输出文件夹路径（如果为None，则在输入文件夹旁创建）
+         headers_level: 标题层级
+         max_chunk_len: 最大块长度
+         supported_extensions: 支持的文件扩展名列表
+         
+     Returns:
+         输出文件夹路径
+         
+     Raises:
+         FileNotFoundError: 输入文件夹不存在
+         RuntimeError: 处理过程中出错
+     """
+     import json
+     from datetime import datetime
+     
+     # 默认支持的文件扩展名
+     if supported_extensions is None:
+         supported_extensions = ['.md', '.txt', '.markdown']
+     
+     # 验证输入文件夹
+     if not os.path.exists(input_folder):
+         raise FileNotFoundError(f"输入文件夹不存在：{input_folder}")
+     
+     if not os.path.isdir(input_folder):
+         raise ValueError(f"输入路径不是文件夹：{input_folder}")
+     
+     # 确定输出文件夹
+     if output_folder is None:
+         timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
+         output_folder = f"{input_folder}_chunks_{timestamp}"
+     
+     # 创建输出文件夹
+     os.makedirs(output_folder, exist_ok=True)
+     
+     # 初始化splitter
+     global pic_temp_path
+     splitter = MarkdownSplitter(pic_temp_path)
+     
+     # 统计信息
+     processed_files = 0
+     total_chunks = 0
+     processing_log = []
+     
+     try:
+         # 遍历输入文件夹中的所有文件
+         for root, dirs, files in os.walk(input_folder):
+             for file in files:
+                 file_path = os.path.join(root, file)
+                 file_name, file_ext = os.path.splitext(file)
+                 
+                 # 检查文件扩展名
+                 if file_ext.lower() not in supported_extensions:
+                     continue
+                 
+                 try:
+                     print(f"正在处理文件: {file_path}")
+                     
+                     # 使用现有的file_split函数处理文件
+                     chunks = splitter.split_file(file_path, headers_level, max_chunk_len)
+                     
+                     if not chunks:
+                         print(f"警告: 文件 {file_path} 没有生成任何chunk")
+                         continue
+                     
+                     # 创建相对路径结构
+                     rel_path = os.path.relpath(root, input_folder)
+                     if rel_path == '.':
+                         output_dir = output_folder
+                     else:
+                         output_dir = os.path.join(output_folder, rel_path)
+                     os.makedirs(output_dir, exist_ok=True)
+                     
+                     # 保存每个chunk为单独的txt文件
+                     file_chunks = 0
+                     for i, chunk in enumerate(chunks):
+                         chunk_filename = f"chunk_{file_name}_{i+1:03d}.txt"
+                         chunk_file_path = os.path.join(output_dir, chunk_filename)
+                         
+                         # 写入chunk内容
+                         with open(chunk_file_path, 'w', encoding='utf-8') as f:
+                             f.write(chunk['content'])
+                         
+                         file_chunks += 1
+                         total_chunks += 1
+                     
+                     processed_files += 1
+                     processing_log.append({
+                         'file_path': file_path,
+                         'chunks_count': file_chunks,
+                         'status': 'success'
+                     })
+                     
+                     print(f"完成处理: {file_path} -> 生成 {file_chunks} 个chunk")
+                     
+                 except Exception as e:
+                     error_msg = f"处理文件 {file_path} 时出错: {str(e)}"
+                     print(f"错误: {error_msg}")
+                     processing_log.append({
+                         'file_path': file_path,
+                         'error': error_msg,
+                         'status': 'error'
+                     })
+                     continue
+         
+         # 生成处理报告
+         report = {
+             'input_folder': input_folder,
+             'output_folder': output_folder,
+             'timestamp': datetime.now().isoformat(),
+             'settings': {
+                 'headers_level': headers_level,
+                 'max_chunk_len': max_chunk_len,
+                 'supported_extensions': supported_extensions
+             },
+             'statistics': {
+                 'processed_files': processed_files,
+                 'total_chunks': total_chunks
+             },
+             'processing_log': processing_log
+         }
+         
+         # 保存处理报告
+         report_file = os.path.join(output_folder, 'processing_report.json')
+         with open(report_file, 'w', encoding='utf-8') as f:
+             json.dump(report, f, ensure_ascii=False, indent=2)
+         
+         # 生成README文件
+         readme_content = f"""# 文本切分处理结果
+ 
+ ## 处理信息
+ - 输入文件夹: {input_folder}
+ - 输出文件夹: {output_folder}
+ - 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
+ 
+ ## 统计信息
+ - 处理文件数: {processed_files}
+ - 生成chunk数: {total_chunks}
+ - 标题层级: {headers_level}
+ - 最大块长度: {max_chunk_len}
+ 
+ ## 支持的文件格式
+ {', '.join(supported_extensions)}
+ 
+ ## 文件命名规则
+ chunk文件命名格式: `chunk_{{原文件名}}_{{序号}}.txt`
+ 
+ ## 详细日志
+ 请查看 `processing_report.json` 文件获取详细的处理日志。
+ """
+         
+         readme_file = os.path.join(output_folder, 'README.md')
+         with open(readme_file, 'w', encoding='utf-8') as f:
+             f.write(readme_content)
+         
+         print(f"\n处理完成!")
+         print(f"输入文件夹: {input_folder}")
+         print(f"输出文件夹: {output_folder}")
+         print(f"处理文件数: {processed_files}")
+         print(f"生成chunk数: {total_chunks}")
+         
+         return output_folder
+         
+     except Exception as e:
+         raise RuntimeError(f"批量处理文件时出错: {str(e)}")
+ 
+ 
+ if __name__ == "__main__":
+     import sys
+     
+     if len(sys.argv) < 2:
+         print("使用方法: python text_split.py <输入文件夹> [输出文件夹] [标题层级] [最大块长度]")
+         print("示例: python text_split.py ./docs ./output_chunks 6 800")
+         sys.exit(1)
+     
+     input_folder = sys.argv[1]
+     output_folder = sys.argv[2] if len(sys.argv) > 2 else None
+     headers_level = int(sys.argv[3]) if len(sys.argv) > 3 else 6
+     max_chunk_len = int(sys.argv[4]) if len(sys.argv) > 4 else 800
+     
+     try:
+         result_folder = main(input_folder, output_folder, headers_level, max_chunk_len)
+         print(f"\n✅ 处理成功完成，结果保存在: {result_folder}")
+     except Exception as e:
+         print(f"\n❌ 处理失败: {str(e)}")
+         sys.exit(1)
+ 
  
  
```

</details>

---

<think><details><summary>Thought Process</summary>我已经成功添加了main函数到text_split.py文件中。让我检查一下是否有任何语法错误。
</details></think>

---



---

<think><details><summary>Thought Process</summary>好的，没有语法错误。让我为用户总结一下我添加的功能。
</details></think>

---

已成功添加main函数到`text_split.py`文件中！这个函数提供了批量处理文件夹的功能。

## 主要功能特点：

### 1. **主函数 `main()`**
- **输入参数**：文件夹路径
- **输出**：处理后的响应文件夹和其中的 `chunk_xxx.txt` 文件

### 2. **核心功能**
- 递归遍历输入文件夹中的所有文件
- 支持多种文件格式：`.md`, `.txt`, `.markdown`
- 将每个文件切分成多个chunk，保存为独立的txt文件
- 保持原文件夹的目录结构

### 3. **输出文件命名规则**
- Chunk文件命名：`chunk_{原文件名}_{序号}.txt`
- 例如：`chunk_readme_001.txt`, `chunk_readme_002.txt`

### 4. **额外功能**
- 自动生成处理报告 (`processing_report.json`)
- 生成说明文档 (`README.md`)
- 统计处理文件数和chunk数
- 错误处理和日志记录

### 5. **使用方式**

#### **作为模块调用**：
```python
from text_split import main
result_folder = main("./input_docs", "./output_chunks")
```

#### **命令行调用**：
```bash
python text_split.py ./input_docs ./output_chunks 6 800
```

参数说明：
- `./input_docs`：输入文件夹路径
- `./output_chunks`：输出文件夹路径（可选）
- `6`：标题层级（可选，默认6）
- `800`：最大块长度（可选，默认800）

### 6. **输出结构示例**
```
output_chunks/
├── README.md
├── processing_report.json  
├── chunk_document1_001.txt
├── chunk_document1_002.txt
├── chunk_document2_001.txt
└── subfolder/
    ├── chunk_doc3_001.txt
    └── chunk_doc3_002.txt
```

现在您可以直接使用这个main函数来批量处理文件夹中的所有文档了！

---

