#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chunk对比工具

用于对比两个chunk脚本（API版本和本地版本）对同一文件的处理结果
"""

import json
import os
import sys
import difflib
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import re

class ChunkComparator:
    """Chunk对比器"""
    
    def __init__(self):
        self.comparison_results = []
        
    def load_json_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        加载JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            解析后的数据，失败返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载文件失败 {file_path}: {e}")
            return None
    
    def extract_chunks(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        提取chunks数据
        
        Args:
            data: JSON数据
            
        Returns:
            chunks列表
        """
        return data.get("chunks", [])
    
    def calculate_text_similarity(self, text1: str, text2: str) -> Dict[str, float]:
        """
        计算两个文本的相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            相似度指标字典
        """
        # 字符级相似度
        char_similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
        
        # 行级相似度
        lines1 = text1.splitlines()
        lines2 = text2.splitlines()
        line_similarity = difflib.SequenceMatcher(None, lines1, lines2).ratio()
        
        # 词级相似度（简单的空格分词）
        words1 = text1.split()
        words2 = text2.split()
        word_similarity = difflib.SequenceMatcher(None, words1, words2).ratio()
        
        return {
            "char_similarity": char_similarity,
            "line_similarity": line_similarity,
            "word_similarity": word_similarity,
            "average_similarity": (char_similarity + line_similarity + word_similarity) / 3
        }
    
    def find_content_overlap(self, chunks1: List[str], chunks2: List[str]) -> Dict[str, Any]:
        """
        寻找内容重叠情况
        
        Args:
            chunks1: 第一组chunks的内容列表
            chunks2: 第二组chunks的内容列表
            
        Returns:
            重叠分析结果
        """
        # 计算每个chunk1在chunks2中的最佳匹配
        matches = []
        for i, chunk1 in enumerate(chunks1):
            best_match = {"index": -1, "similarity": 0, "chunk2_index": -1}
            for j, chunk2 in enumerate(chunks2):
                similarity = self.calculate_text_similarity(chunk1, chunk2)["average_similarity"]
                if similarity > best_match["similarity"]:
                    best_match = {
                        "index": i,
                        "similarity": similarity,
                        "chunk2_index": j
                    }
            matches.append(best_match)
        
        # 计算覆盖率
        high_similarity_matches = [m for m in matches if m["similarity"] > 0.8]
        coverage_rate = len(high_similarity_matches) / len(chunks1) if chunks1 else 0
        
        return {
            "matches": matches,
            "coverage_rate": coverage_rate,
            "high_similarity_count": len(high_similarity_matches),
            "total_chunks1": len(chunks1),
            "total_chunks2": len(chunks2)
        }
    
    def analyze_chunk_distribution(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析chunk分布情况
        
        Args:
            chunks: chunks列表
            
        Returns:
            分布分析结果
        """
        if not chunks:
            return {"total": 0, "sizes": [], "avg_size": 0, "min_size": 0, "max_size": 0}
        
        sizes = [len(chunk.get("content", "")) for chunk in chunks]
        
        return {
            "total": len(chunks),
            "sizes": sizes,
            "avg_size": sum(sizes) / len(sizes),
            "min_size": min(sizes),
            "max_size": max(sizes),
            "total_content_length": sum(sizes)
        }
    
    def compare_chunks(self, file1_path: str, file2_path: str) -> Dict[str, Any]:
        """
        对比两个chunk文件
        
        Args:
            file1_path: 第一个文件路径
            file2_path: 第二个文件路径
            
        Returns:
            对比结果
        """
        # 加载文件
        data1 = self.load_json_file(file1_path)
        data2 = self.load_json_file(file2_path)
        
        if not data1 or not data2:
            return {"error": "文件加载失败"}
        
        # 提取基本信息
        source_file1 = data1.get("source_file", "unknown")
        source_file2 = data2.get("source_file", "unknown")
        method1 = data1.get("processing_method", "unknown")
        method2 = data2.get("processing_method", "unknown")
        
        # 提取chunks
        chunks1 = self.extract_chunks(data1)
        chunks2 = self.extract_chunks(data2)
        
        # 提取内容列表
        contents1 = [chunk.get("content", "") for chunk in chunks1]
        contents2 = [chunk.get("content", "") for chunk in chunks2]
        
        # 分析chunk分布
        dist1 = self.analyze_chunk_distribution(chunks1)
        dist2 = self.analyze_chunk_distribution(chunks2)
        
        # 寻找内容重叠
        overlap = self.find_content_overlap(contents1, contents2)
        
        # 计算整体相似度（合并所有内容）
        full_content1 = "\n".join(contents1)
        full_content2 = "\n".join(contents2)
        overall_similarity = self.calculate_text_similarity(full_content1, full_content2)
        
        # 详细chunk对比
        detailed_comparisons = []
        max_compare = min(len(chunks1), len(chunks2), 10)  # 限制详细对比数量
        for i in range(max_compare):
            chunk1 = chunks1[i] if i < len(chunks1) else {"content": "", "chunk_size": 0}
            chunk2 = chunks2[i] if i < len(chunks2) else {"content": "", "chunk_size": 0}
            
            content1 = chunk1.get("content", "")
            content2 = chunk2.get("content", "")
            
            similarity = self.calculate_text_similarity(content1, content2)
            
            detailed_comparisons.append({
                "chunk_index": i + 1,
                "chunk1_size": len(content1),
                "chunk2_size": len(content2),
                "size_diff": abs(len(content1) - len(content2)),
                "similarity": similarity,
                "is_similar": similarity["average_similarity"] > 0.8
            })
        
        return {
            "comparison_time": datetime.now().isoformat(),
            "files": {
                "file1": {"path": file1_path, "source": source_file1, "method": method1},
                "file2": {"path": file2_path, "source": source_file2, "method": method2}
            },
            "source_files_match": source_file1 == source_file2,
            "chunk_counts": {
                "file1_chunks": len(chunks1),
                "file2_chunks": len(chunks2),
                "count_diff": abs(len(chunks1) - len(chunks2)),
                "count_ratio": len(chunks2) / len(chunks1) if len(chunks1) > 0 else 0
            },
            "content_analysis": {
                "file1_distribution": dist1,
                "file2_distribution": dist2,
                "total_content_size_diff": abs(dist1["total_content_length"] - dist2["total_content_length"]),
                "avg_chunk_size_diff": abs(dist1["avg_size"] - dist2["avg_size"])
            },
            "similarity_metrics": {
                "overall": overall_similarity,
                "content_overlap": overlap,
                "detailed_comparisons": detailed_comparisons
            },
            "quality_scores": {
                "coverage_score": overlap["coverage_rate"],
                "similarity_score": overall_similarity["average_similarity"],
                "size_consistency": 1 - (abs(dist1["avg_size"] - dist2["avg_size"]) / max(dist1["avg_size"], dist2["avg_size"], 1)),
                "chunk_count_consistency": 1 - (abs(len(chunks1) - len(chunks2)) / max(len(chunks1), len(chunks2), 1))
            }
        }
    
    def generate_comparison_report(self, comparison_result: Dict[str, Any]) -> str:
        """
        生成对比报告
        
        Args:
            comparison_result: 对比结果
            
        Returns:
            格式化的报告文本
        """
        if "error" in comparison_result:
            return f"❌ 对比失败: {comparison_result['error']}"
        
        files = comparison_result["files"]
        chunk_counts = comparison_result["chunk_counts"]
        content_analysis = comparison_result["content_analysis"]
        similarity = comparison_result["similarity_metrics"]
        scores = comparison_result["quality_scores"]
        
        report = f"""
📊 Chunk对比报告
{'='*60}
🕐 对比时间: {comparison_result['comparison_time']}

📁 文件信息:
  文件1: {os.path.basename(files['file1']['path'])} [{files['file1']['method']}]
  文件2: {os.path.basename(files['file2']['path'])} [{files['file2']['method']}]
  源文件匹配: {'✅ 是' if comparison_result['source_files_match'] else '❌ 否'}

📈 Chunk数量对比:
  文件1 chunks: {chunk_counts['file1_chunks']}
  文件2 chunks: {chunk_counts['file2_chunks']}
  数量差异: {chunk_counts['count_diff']}
  数量比率: {chunk_counts['count_ratio']:.3f}

📏 内容大小分析:
  文件1总内容: {content_analysis['file1_distribution']['total_content_length']:,} 字符
  文件2总内容: {content_analysis['file2_distribution']['total_content_length']:,} 字符
  总大小差异: {content_analysis['total_content_size_diff']:,} 字符
  
  文件1平均chunk: {content_analysis['file1_distribution']['avg_size']:.1f} 字符
  文件2平均chunk: {content_analysis['file2_distribution']['avg_size']:.1f} 字符
  平均大小差异: {content_analysis['avg_chunk_size_diff']:.1f} 字符

🔍 相似度分析:
  整体相似度:
    字符级: {similarity['overall']['char_similarity']:.3f}
    行级: {similarity['overall']['line_similarity']:.3f}
    词级: {similarity['overall']['word_similarity']:.3f}
    平均: {similarity['overall']['average_similarity']:.3f}
  
  内容覆盖率: {similarity['content_overlap']['coverage_rate']:.3f}
  高相似度匹配: {similarity['content_overlap']['high_similarity_count']}/{similarity['content_overlap']['total_chunks1']}

📊 质量评分:
  内容覆盖分数: {scores['coverage_score']:.3f} ({'excellent' if scores['coverage_score'] > 0.9 else 'good' if scores['coverage_score'] > 0.7 else 'fair' if scores['coverage_score'] > 0.5 else 'poor'})
  相似度分数: {scores['similarity_score']:.3f} ({'excellent' if scores['similarity_score'] > 0.9 else 'good' if scores['similarity_score'] > 0.7 else 'fair' if scores['similarity_score'] > 0.5 else 'poor'})
  大小一致性: {scores['size_consistency']:.3f} ({'excellent' if scores['size_consistency'] > 0.9 else 'good' if scores['size_consistency'] > 0.7 else 'fair' if scores['size_consistency'] > 0.5 else 'poor'})
  数量一致性: {scores['chunk_count_consistency']:.3f} ({'excellent' if scores['chunk_count_consistency'] > 0.9 else 'good' if scores['chunk_count_consistency'] > 0.7 else 'fair' if scores['chunk_count_consistency'] > 0.5 else 'poor'})

🔬 详细Chunk对比 (前10个):"""
        
        for comp in similarity['detailed_comparisons']:
            status = "✅" if comp['is_similar'] else "⚠️"
            report += f"""
  Chunk {comp['chunk_index']}: {status}
    大小: {comp['chunk1_size']} vs {comp['chunk2_size']} (差异: {comp['size_diff']})
    相似度: {comp['similarity']['average_similarity']:.3f}"""
        
        # 总结评价
        overall_score = (scores['coverage_score'] + scores['similarity_score'] + 
                        scores['size_consistency'] + scores['chunk_count_consistency']) / 4
        
        if overall_score > 0.9:
            conclusion = "🎉 两种方法的切分结果非常一致，质量优秀"
        elif overall_score > 0.7:
            conclusion = "👍 两种方法的切分结果较为一致，质量良好"
        elif overall_score > 0.5:
            conclusion = "⚠️ 两种方法的切分结果存在一定差异，需要关注"
        else:
            conclusion = "❌ 两种方法的切分结果差异较大，需要深入分析"
        
        report += f"""

🎯 综合评价:
  总体得分: {overall_score:.3f}
  评价结论: {conclusion}

💡 建议:"""
        
        if scores['coverage_score'] < 0.7:
            report += "\n  • 内容覆盖率偏低，建议检查切分逻辑差异"
        if scores['similarity_score'] < 0.7:
            report += "\n  • 内容相似度偏低，建议检查文本处理差异"
        if scores['size_consistency'] < 0.7:
            report += "\n  • chunk大小差异较大，建议检查切分粒度参数"
        if scores['chunk_count_consistency'] < 0.7:
            report += "\n  • chunk数量差异较大，建议检查切分策略差异"
        
        if overall_score > 0.9:
            report += "\n  • 两种方法结果一致性很好，可以互相验证"
        
        return report
    
    def batch_compare_folders(self, folder1: str, folder2: str, output_file: str = None) -> Dict[str, Any]:
        """
        批量对比两个文件夹中的chunk文件
        
        Args:
            folder1: 第一个文件夹路径（如API版本输出）
            folder2: 第二个文件夹路径（如本地版本输出）
            output_file: 输出报告文件路径
            
        Returns:
            批量对比结果
        """
        if not os.path.exists(folder1) or not os.path.exists(folder2):
            return {"error": f"文件夹不存在: {folder1} 或 {folder2}"}
        
        print(f"📁 开始批量对比文件夹:")
        print(f"  文件夹1: {folder1}")
        print(f"  文件夹2: {folder2}")
        
        # 收集两个文件夹中的JSON文件
        files1 = {}
        files2 = {}
        
        # 扫描第一个文件夹
        for file in os.listdir(folder1):
            if file.endswith('_chunks.json'):
                base_name = file.replace('_chunks.json', '')
                files1[base_name] = os.path.join(folder1, file)
        
        # 扫描第二个文件夹
        for file in os.listdir(folder2):
            if file.endswith('_chunks.json'):
                base_name = file.replace('_chunks.json', '')
                files2[base_name] = os.path.join(folder2, file)
        
        print(f"📊 文件统计:")
        print(f"  文件夹1中的chunk文件: {len(files1)} 个")
        print(f"  文件夹2中的chunk文件: {len(files2)} 个")
        
        # 找到共同的文件
        common_files = set(files1.keys()) & set(files2.keys())
        only_in_folder1 = set(files1.keys()) - set(files2.keys())
        only_in_folder2 = set(files2.keys()) - set(files1.keys())
        
        print(f"  共同文件: {len(common_files)} 个")
        if only_in_folder1:
            print(f"  仅在文件夹1: {len(only_in_folder1)} 个")
            for name in sorted(list(only_in_folder1)[:5]):  # 只显示前5个
                print(f"    - {name}")
            if len(only_in_folder1) > 5:
                print(f"    ... 还有 {len(only_in_folder1) - 5} 个")
        
        if only_in_folder2:
            print(f"  仅在文件夹2: {len(only_in_folder2)} 个")
            for name in sorted(list(only_in_folder2)[:5]):  # 只显示前5个
                print(f"    - {name}")
            if len(only_in_folder2) > 5:
                print(f"    ... 还有 {len(only_in_folder2) - 5} 个")
        
        if not common_files:
            return {
                "error": "未找到可对比的文件对",
                "details": {
                    "folder1_files": len(files1),
                    "folder2_files": len(files2),
                    "only_in_folder1": list(only_in_folder1),
                    "only_in_folder2": list(only_in_folder2)
                }
            }
        
        print(f"\n🔍 开始对比 {len(common_files)} 个文件对...")
        
        batch_results = []
        summary_stats = {
            "total_comparisons": 0,
            "avg_coverage_score": 0,
            "avg_similarity_score": 0,
            "avg_size_consistency": 0,
            "avg_chunk_count_consistency": 0,
            "excellent_count": 0,
            "good_count": 0,
            "fair_count": 0,
            "poor_count": 0
        }
        
        for i, base_name in enumerate(sorted(common_files), 1):
            print(f"[{i}/{len(common_files)}] 对比文件: {base_name}")
            
            result = self.compare_chunks(files1[base_name], files2[base_name])
            
            if "error" not in result:
                scores = result["quality_scores"]
                overall_score = (scores['coverage_score'] + scores['similarity_score'] + 
                               scores['size_consistency'] + scores['chunk_count_consistency']) / 4
                
                result["overall_score"] = overall_score
                result["base_name"] = base_name
                
                # 更新统计
                summary_stats["total_comparisons"] += 1
                summary_stats["avg_coverage_score"] += scores['coverage_score']
                summary_stats["avg_similarity_score"] += scores['similarity_score']
                summary_stats["avg_size_consistency"] += scores['size_consistency']
                summary_stats["avg_chunk_count_consistency"] += scores['chunk_count_consistency']
                
                if overall_score > 0.9:
                    summary_stats["excellent_count"] += 1
                elif overall_score > 0.7:
                    summary_stats["good_count"] += 1
                elif overall_score > 0.5:
                    summary_stats["fair_count"] += 1
                else:
                    summary_stats["poor_count"] += 1
            
            batch_results.append(result)
        
        # 计算平均值
        if summary_stats["total_comparisons"] > 0:
            for key in ["avg_coverage_score", "avg_similarity_score", "avg_size_consistency", "avg_chunk_count_consistency"]:
                summary_stats[key] /= summary_stats["total_comparisons"]
        
        batch_summary = {
            "comparison_time": datetime.now().isoformat(),
            "folders": {"folder1": folder1, "folder2": folder2},
            "summary_stats": summary_stats,
            "detailed_results": batch_results
        }
        
        # 生成批量报告
        batch_report = self.generate_batch_report(batch_summary)
        
        # 保存报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(batch_report)
            print(f"📄 批量对比报告已保存: {output_file}")
        
        return batch_summary
    
    def generate_batch_report(self, batch_summary: Dict[str, Any]) -> str:
        """生成批量对比报告"""
        stats = batch_summary["summary_stats"]
        
        report = f"""
📊 批量Chunk对比报告
{'='*80}
🕐 对比时间: {batch_summary['comparison_time']}

📁 对比文件夹:
  文件夹1: {batch_summary['folders']['folder1']}
  文件夹2: {batch_summary['folders']['folder2']}

📈 总体统计:
  对比文件数: {stats['total_comparisons']}
  
📊 质量分布:
  优秀 (>0.9): {stats['excellent_count']} 个 ({stats['excellent_count']/stats['total_comparisons']*100:.1f}%)
  良好 (0.7-0.9): {stats['good_count']} 个 ({stats['good_count']/stats['total_comparisons']*100:.1f}%)
  一般 (0.5-0.7): {stats['fair_count']} 个 ({stats['fair_count']/stats['total_comparisons']*100:.1f}%)
  较差 (<0.5): {stats['poor_count']} 个 ({stats['poor_count']/stats['total_comparisons']*100:.1f}%)

📏 平均指标:
  内容覆盖分数: {stats['avg_coverage_score']:.3f}
  相似度分数: {stats['avg_similarity_score']:.3f}
  大小一致性: {stats['avg_size_consistency']:.3f}
  数量一致性: {stats['avg_chunk_count_consistency']:.3f}

📋 详细结果:"""
        
        for result in batch_summary["detailed_results"]:
            if "error" in result:
                report += f"\n  ❌ {result.get('base_name', 'unknown')}: {result['error']}"
            else:
                score = result["overall_score"]
                status = "🎉" if score > 0.9 else "👍" if score > 0.7 else "⚠️" if score > 0.5 else "❌"
                report += f"\n  {status} {result['base_name']}: {score:.3f}"
        
        overall_avg = (stats['avg_coverage_score'] + stats['avg_similarity_score'] + 
                      stats['avg_size_consistency'] + stats['avg_chunk_count_consistency']) / 4
        
        report += f"""

🎯 总体评价:
  平均综合得分: {overall_avg:.3f}
  两种方法的切分一致性: {'优秀' if overall_avg > 0.9 else '良好' if overall_avg > 0.7 else '一般' if overall_avg > 0.5 else '较差'}
"""
        
        return report


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  单文件对比: python chunk_comparator.py compare <file1> <file2>")
        print("  批量对比: python chunk_comparator.py batch <folder1> <folder2> [output_report]")
        print()
        print("示例:")
        print("  python chunk_comparator.py compare api_output.json local_output.json")
        print("  python chunk_comparator.py batch ./api_results ./local_results batch_report.txt")
        sys.exit(1)
    
    comparator = ChunkComparator()
    command = sys.argv[1]
    
    if command == "compare" and len(sys.argv) >= 4:
        file1, file2 = sys.argv[2], sys.argv[3]
        print(f"🔍 对比chunk文件:")
        print(f"  文件1: {file1}")
        print(f"  文件2: {file2}")
        
        result = comparator.compare_chunks(file1, file2)
        report = comparator.generate_comparison_report(result)
        print(report)
        
        # 保存详细结果
        result_file = f"comparison_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细结果已保存: {result_file}")
        
    elif command == "batch" and len(sys.argv) >= 4:
        folder1, folder2 = sys.argv[2], sys.argv[3]
        output_file = sys.argv[4] if len(sys.argv) > 4 else f"batch_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        print(f"🔍 批量对比文件夹:")
        print(f"  文件夹1: {folder1}")
        print(f"  文件夹2: {folder2}")
        
        result = comparator.batch_compare_folders(folder1, folder2, output_file)
        
        if "error" in result:
            print(f"❌ 批量对比失败: {result['error']}")
        else:
            report = comparator.generate_batch_report(result)
            print(report)
    
    else:
        print("❌ 无效的命令或参数不足")
        sys.exit(1)


if __name__ == "__main__":
    main()
