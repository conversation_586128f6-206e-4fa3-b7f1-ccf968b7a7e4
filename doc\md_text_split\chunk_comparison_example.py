#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chunk对比工具使用示例

演示如何使用chunk_comparator进行文件对比和结果分析
"""

import os
import json
from datetime import datetime
from chunk_comparator import ChunkComparator

def create_sample_api_chunk():
    """创建示例API版本chunk文件"""
    api_chunk = {
        "source_file": "example_document.txt",
        "source_path": "/path/to/example_document.txt",
        "processing_time": datetime.now().isoformat(),
        "processing_method": "api",
        "api_response": {
            "bo": [
                [
                    "第一章 产品概述\n\n本产品是一款高性能的网络设备，具有以下特点：",
                    "1. 高性能处理能力\n2. 低延迟传输\n3. 可靠性保证",
                    "第二章 技术规格\n\n处理器：高性能多核芯片\n内存：16GB DDR4",
                    "存储：1TB SSD\n网络接口：10GbE光口"
                ]
            ],
            "code": {"code": "0000", "msg": "成功", "msgId": "Success"}
        },
        "settings": {
            "headers_level": 6,
            "chunk_max_len": 2048,
            "add_filename": True
        },
        "total_chunks": 4,
        "chunks": [
            {
                "chunk_id": 1,
                "content": "第一章 产品概述\n\n本产品是一款高性能的网络设备，具有以下特点：",
                "metadata": {},
                "title": "",
                "chunk_size": 36,
                "supplement": "",
                "images": []
            },
            {
                "chunk_id": 2,
                "content": "1. 高性能处理能力\n2. 低延迟传输\n3. 可靠性保证",
                "metadata": {},
                "title": "",
                "chunk_size": 30,
                "supplement": "",
                "images": []
            },
            {
                "chunk_id": 3,
                "content": "第二章 技术规格\n\n处理器：高性能多核芯片\n内存：16GB DDR4",
                "metadata": {},
                "title": "",
                "chunk_size": 32,
                "supplement": "",
                "images": []
            },
            {
                "chunk_id": 4,
                "content": "存储：1TB SSD\n网络接口：10GbE光口",
                "metadata": {},
                "title": "",
                "chunk_size": 22,
                "supplement": "",
                "images": []
            }
        ]
    }
    return api_chunk

def create_sample_local_chunk():
    """创建示例本地版本chunk文件"""
    local_chunk = {
        "source_file": "example_document.txt",
        "source_path": "/path/to/example_document.txt", 
        "processing_time": datetime.now().isoformat(),
        "processing_method": "local",
        "api_response": None,
        "settings": {
            "headers_level": 6,
            "chunk_max_len": 2048,
            "add_filename": True
        },
        "total_chunks": 3,
        "chunks": [
            {
                "chunk_id": 1,
                "content": "第一章 产品概述\n\n本产品是一款高性能的网络设备，具有以下特点：\n1. 高性能处理能力\n2. 低延迟传输",
                "metadata": {"header": "第一章 产品概述"},
                "title": "",
                "chunk_size": 52,
                "supplement": "从产品概述章节提取",
                "images": []
            },
            {
                "chunk_id": 2, 
                "content": "3. 可靠性保证\n\n第二章 技术规格\n\n处理器：高性能多核芯片",
                "metadata": {"header": "第二章 技术规格"},
                "title": "",
                "chunk_size": 33,
                "supplement": "跨章节内容",
                "images": []
            },
            {
                "chunk_id": 3,
                "content": "内存：16GB DDR4\n存储：1TB SSD\n网络接口：10GbE光口",
                "metadata": {"header": "技术规格详情"},
                "title": "",
                "chunk_size": 32,
                "supplement": "技术规格部分",
                "images": []
            }
        ]
    }
    return local_chunk

def demo_single_file_comparison():
    """演示单文件对比功能"""
    print("=" * 60)
    print("📊 单文件对比示例")
    print("=" * 60)
    
    # 创建示例数据
    api_data = create_sample_api_chunk()
    local_data = create_sample_local_chunk()
    
    # 保存到临时文件
    api_file = "temp_api_example.json"
    local_file = "temp_local_example.json"
    
    with open(api_file, 'w', encoding='utf-8') as f:
        json.dump(api_data, f, ensure_ascii=False, indent=2)
    
    with open(local_file, 'w', encoding='utf-8') as f:
        json.dump(local_data, f, ensure_ascii=False, indent=2)
    
    print(f"📝 创建示例文件:")
    print(f"  API版本: {api_file}")
    print(f"  本地版本: {local_file}")
    
    # 进行对比
    comparator = ChunkComparator()
    result = comparator.compare_chunks(api_file, local_file)
    
    # 生成报告
    report = comparator.generate_comparison_report(result)
    print(report)
    
    # 清理临时文件
    os.remove(api_file)
    os.remove(local_file)
    
    return result

def demo_detailed_analysis(comparison_result):
    """演示详细分析功能"""
    print("\n" + "=" * 60)
    print("🔬 详细分析示例")
    print("=" * 60)
    
    if "error" in comparison_result:
        print("❌ 无法进行详细分析：存在错误")
        return
    
    # 提取关键指标
    scores = comparison_result["quality_scores"]
    similarity = comparison_result["similarity_metrics"]
    chunk_counts = comparison_result["chunk_counts"]
    
    print("📈 关键指标分析:")
    print(f"  综合质量得分: {(scores['coverage_score'] + scores['similarity_score'] + scores['size_consistency'] + scores['chunk_count_consistency']) / 4:.3f}")
    
    # 分析各个维度
    dimensions = [
        ("内容覆盖", scores['coverage_score']),
        ("相似度", scores['similarity_score']),
        ("大小一致性", scores['size_consistency']),
        ("数量一致性", scores['chunk_count_consistency'])
    ]
    
    print("\n📊 各维度得分:")
    for name, score in dimensions:
        level = "优秀" if score > 0.9 else "良好" if score > 0.7 else "一般" if score > 0.5 else "较差"
        bar = "█" * int(score * 20) + "░" * (20 - int(score * 20))
        print(f"  {name:8}: {score:.3f} [{bar}] {level}")
    
    # 分析chunk数量差异
    print(f"\n📏 Chunk数量分析:")
    print(f"  API版本chunks: {chunk_counts['file1_chunks']}")
    print(f"  本地版本chunks: {chunk_counts['file2_chunks']}")
    print(f"  数量差异: {chunk_counts['count_diff']}")
    
    if chunk_counts['count_diff'] > 0:
        if chunk_counts['file1_chunks'] > chunk_counts['file2_chunks']:
            print("  💡 API版本切分更细，可能包含更多细节")
        else:
            print("  💡 本地版本切分更细，可能保留更多结构信息")
    
    # 分析相似度模式
    print(f"\n🔍 相似度模式分析:")
    overall = similarity['overall']
    print(f"  字符级相似度: {overall['char_similarity']:.3f}")
    print(f"  行级相似度: {overall['line_similarity']:.3f}")
    print(f"  词级相似度: {overall['word_similarity']:.3f}")
    
    # 最高和最低相似度
    char_sim = overall['char_similarity']
    line_sim = overall['line_similarity']
    word_sim = overall['word_similarity']
    
    if char_sim == max(char_sim, line_sim, word_sim):
        print("  📝 字符级相似度最高：内容字面相似性好")
    elif line_sim == max(char_sim, line_sim, word_sim):
        print("  📄 行级相似度最高：结构组织相似")
    else:
        print("  📖 词级相似度最高：语义内容相似")
    
    # 覆盖率分析
    overlap = similarity['content_overlap']
    print(f"\n📋 内容覆盖分析:")
    print(f"  覆盖率: {overlap['coverage_rate']:.3f}")
    print(f"  高相似匹配: {overlap['high_similarity_count']}/{overlap['total_chunks1']}")
    
    if overlap['coverage_rate'] < 0.8:
        print("  ⚠️ 覆盖率偏低，建议检查内容是否有遗漏")
    
    # 改进建议
    print(f"\n💡 优化建议:")
    
    if scores['coverage_score'] < 0.7:
        print("  🔍 内容覆盖率偏低：")
        print("    - 检查两种方法的切分逻辑是否一致")
        print("    - 确认是否有内容被忽略或重复处理")
    
    if scores['similarity_score'] < 0.7:
        print("  📝 内容相似度偏低：")
        print("    - 检查文本预处理步骤的差异")
        print("    - 确认格式化和清理规则是否统一")
    
    if scores['chunk_count_consistency'] < 0.7:
        print("  📊 数量一致性偏低：")
        print("    - 调整切分参数，使chunk大小更接近")
        print("    - 统一切分策略和标准")

def demo_best_practices():
    """演示最佳实践"""
    print("\n" + "=" * 60)
    print("💡 最佳实践建议")
    print("=" * 60)
    
    practices = [
        "1. 定期对比验证",
        "   - 设置自动化对比流程",
        "   - 监控关键质量指标变化",
        "   - 建立质量基线和阈值",
        "",
        "2. 问题诊断流程",
        "   - 优先检查综合得分低的文件",
        "   - 分析具体维度的问题原因",
        "   - 结合人工检查验证结果",
        "",
        "3. 参数优化策略",
        "   - 根据对比结果调整切分参数",
        "   - 测试不同配置的效果",
        "   - 保持两种方法参数的一致性",
        "",
        "4. 质量监控",
        "   - 建立质量仪表板",
        "   - 设置质量下降告警",
        "   - 定期生成质量报告"
    ]
    
    for practice in practices:
        print(practice)

def main():
    """主演示函数"""
    print("🎯 Chunk对比工具完整示例")
    print("展示如何使用chunk_comparator进行结果对比和分析")
    
    # 1. 单文件对比演示
    result = demo_single_file_comparison()
    
    # 2. 详细分析演示
    demo_detailed_analysis(result)
    
    # 3. 最佳实践建议
    demo_best_practices()
    
    print("\n" + "=" * 60)
    print("✅ 示例演示完成")
    print("=" * 60)
    print("💻 实际使用命令:")
    print("  单文件对比: python chunk_comparator.py compare file1.json file2.json")
    print("  批量对比: python chunk_comparator.py batch folder1 folder2")
    print("📖 详细说明请参考: chunk对比工具使用说明.md")

if __name__ == "__main__":
    main()
