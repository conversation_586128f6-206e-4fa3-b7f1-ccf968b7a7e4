# 文档分割工具使用说明

## 功能概述
这是一个多线程文档分割工具，通过调用API接口将大文档切分为小块，支持并行处理多个文件。

## 使用方式

### 方式1: 命令行参数
```bash
python split_api_test.py "D:\your\document\folder"
```

### 方式2: 环境变量
```bash
# Windows
set DOC_SPLIT_INPUT_FOLDER=D:\your\document\folder
python split_api_test.py

# Linux/Mac
export DOC_SPLIT_INPUT_FOLDER=/path/to/your/documents
python split_api_test.py
```

### 方式3: 配置文件
创建 `split_config.txt` 文件，内容为文件夹路径：
```
D:\your\document\folder
```
然后运行：
```bash
python split_api_test.py
```

### 方式4: 交互式输入
直接运行脚本，按提示输入路径：
```bash
python split_api_test.py
```

### 方式5: 默认路径
如果以上方式都没有指定路径，将使用代码中的默认路径。

## 配置参数

### 基本参数
- **输入文件夹路径**: 要处理的文档所在文件夹
- **输出文件夹路径**: 结果保存位置（None表示自动生成）
- **标题层级**: Markdown标题层级（默认6）
- **最大块长度**: 每个切片的最大字符数（默认2048）
- **添加文件名**: 是否在切片中添加原文件名（默认True）
- **支持的文件格式**: 支持多种文本格式，包括 .txt, .md, .markdown, .text, .rtf, .log, .csv, .xml, .json, .html, .py, .js, .css, .sql, .yaml, .yml, .ini, .cfg, .conf 等
- **并行线程数**: 同时处理的线程数量（默认10）

### API配置
- **API地址**: `https://llm.dt.zte.com.cn/zte-ibo-acm-tools/cut`
- **超时时间**: 120秒

## 输出结果

### 文件结构
```
输出文件夹/
├── 文件1_chunks.json
├── 文件2_chunks.json
├── ...
└── processing_report.json
```

### 统一JSON格式
两个脚本（API版本和本地版本）的输出格式完全一致，包含所有字段：

```json
{
  "source_file": "原文件名.txt",
  "source_path": "原文件完整路径",
  "processing_time": "2025-01-03T10:00:00",
  "processing_method": "api",
  "api_response": {
    "bo": [
      [
        "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n\n| 版本 | 日期 | 作者 | 审核者 | 备注 |",
        "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n\n表目录\n表 1-1 ZXMP M721系列配置指导书清单4\n",
        "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n# 编写目的\n为方便大家使用ZXMP M721系列配置指导书"
      ]
    ],
    "code": {
      "code": "0000",
      "msg": "成功",
      "msgId": "Success"
    }
  },
  "settings": {
    "headers_level": 6,
    "chunk_max_len": 2048,
    "add_filename": true
  },
  "total_chunks": 3,
  "chunks": [
    {
      "chunk_id": 1,
      "content": "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n\n| 版本 | 日期 | 作者 | 审核者 | 备注 |",
      "metadata": {},
      "title": "",
      "chunk_size": 58,
      "supplement": "",
      "images": []
    },
    {
      "chunk_id": 2,
      "content": "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n\n表目录\n表 1-1 ZXMP M721系列配置指导书清单4\n",
      "metadata": {},
      "title": "",
      "chunk_size": 66,
      "supplement": "",
      "images": []
    },
    {
      "chunk_id": 3,
      "content": "00-ZXMP M721系列配置指导书使用说明_20250818_CN\n# 编写目的\n为方便大家使用ZXMP M721系列配置指导书",
      "metadata": {},
      "title": "",
      "chunk_size": 63,
      "supplement": "",
      "images": []
    }
  ]
}
```

### 字段说明

#### 顶级字段
- **`source_file`**: 原始文件名
- **`source_path`**: 原始文件完整路径
- **`processing_time`**: 处理时间（ISO格式）
- **`processing_method`**: 处理方式（"api" 或 "local"）
- **`api_response`**: API响应数据（仅API版本有效，本地版本为null）
- **`settings`**: 处理参数配置
- **`total_chunks`**: 总切片数量
- **`chunks`**: 切片数组

#### 切片字段（统一格式）
- **`chunk_id`**: 切片ID（从1开始）
- **`content`**: 切片内容文本
- **`metadata`**: 元数据信息
- **`title`**: 切片标题（API版本暂为空，本地版本可包含标题信息）
- **`chunk_size`**: 切片大小（字符数）
- **`supplement`**: 补充信息（本地版本使用，API版本为空）
- **`images`**: 图片信息数组（本地版本使用，API版本为空数组）

#### 字段兼容性
- API版本：`api_response`有值，`supplement`为空，`images`为空数组
- 本地版本：`api_response`为null，`supplement`和`images`可能有值

## 监控和日志

### 进度显示
```
[56/89] [线程:APISplitter_6] 📄 正在处理文件: 文件名.txt
>>> 整体进度: 46/89 (51.7%) 已完成 <<<
```

### 状态类型
- ✅ **success**: 处理成功
- ❌ **error**: 处理失败
- ⚠️ **warning**: 有警告（如API返回空数据）

### 处理报告
详细的处理日志保存在 `processing_report.json` 中，包含：
- 处理统计信息
- 每个文件的处理结果
- 错误和警告详情
- 线程使用情况

## 常见问题

### 1. API返回空数据警告
**原因**:
- 文件内容格式不适合API处理
- API服务器临时故障
- 网络连接问题

**解决方法**:
- 检查文件内容是否有效
- 确认网络连接正常
- 检查文件编码是否为UTF-8

### 2. 处理速度慢
**优化建议**:
- 调整 `并行线程数` 参数
- 检查网络延迟
- 考虑使用本地API服务

### 3. 内存使用过高
**解决方法**:
- 减少并行线程数
- 减小最大块长度
- 分批处理文件

## 性能特点

- 🚀 **多线程并行**: 支持最多10个线程同时处理
- 🌐 **API切分**: 使用远程API，结果更准确
- 📊 **详细报告**: 生成完整的处理报告
- 🔒 **线程安全**: 支持并发处理多个文件
- ⏱️ **实时进度**: 显示详细的处理进度
