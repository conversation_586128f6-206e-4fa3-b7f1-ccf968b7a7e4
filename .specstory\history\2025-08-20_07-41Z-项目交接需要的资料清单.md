<!-- Generated by SpecStory -->

# 项目交接需要的资料清单 (2025-08-20 07:41Z)

_**User**_

这个项目是刚交接过来的，我需要他们交接给我哪些东西？
可能有这些东西：
1、 代码  
2、需求、方案文档   
3、工具：1）infogo数据处理脚本，2）文档信息处理脚本   3）转KG的脚本   4）解析切分脚本； 
4、系统账号、密码


---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户刚接手了一个项目，想知道需要交接哪些东西。从项目结构来看，这是一个AI/RAG系统的项目，涉及知识图谱、搜索、重排序等功能。

用户提到的需要交接的内容包括：
1. 代码
2. 需求、方案文档
3. 工具脚本
4. 系统账号密码

我需要分析项目结构，查看现有的文档和代码，帮助用户整理出完整的交接清单。

从项目结构可以看到：
- 有代码结构（各种服务、工具类、配置等）
- 有文档目录（doc/）
- 有一些脚本和工具
- 有配置文件

我需要详细分析这些内容，给出完整的交接清单。</details></think>

---

我来帮你分析这个项目的交接内容。让我先查看项目的整体情况和相关文档。

---

Could not find file 'README.md' in the workspace.

---

Read file: undefined

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>现在我看到了主要的项目信息。让我继续查看一些关键的配置文件和文档，以便给出更完整的交接清单。</details></think>

---

现在让我查看一些关键的配置文件和文档，以及项目的核心功能模块：

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>让我再看一些关键脚本和配置文件，以便给出更完整的交接清单。</details></think>

---

让我继续查看一些关键的脚本和工具，以及系统配置信息：

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

Read file: undefined

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>看起来我已经收集了足够的信息来给用户提供一个完整的交接清单。让我整理一下这个项目需要交接的所有内容。

这是一个ZTE的AI产品问答系统，基于RAG架构，支持多种数据源检索和知识图谱查询。从项目结构和配置可以看出：

1. 代码结构完整，包含各个功能模块
2. 有详细的技术文档
3. 有监控系统
4. 有各种配置文件和脚本
5. 需要多个外部系统的账号和配置

让我创建一个详细的交接清单。</details></think>

---

基于我对项目的分析，我为你整理了一个完整的交接清单。这是一个ZTE的AI产品问答系统，基于RAG架构。以下是需要交接的所有内容：

## 📋 ZTE产品技术问答系统交接清单

### 1️⃣ **代码交接**
✅ **已有的代码模块：**
- **主服务**：`main.py` (Flask应用，监听端口10023)
- **控制器**：`controller/` (接口控制器)
- **业务服务**：`service/` (核心业务逻辑)  
- **数据检索**：`retrieve/` (ES、Milvus、知识图谱检索)
- **重排序**：`rerank/` (BGE-M3重排序模型)
- **工具类**：`utils/` (各种工具函数)
- **领域模型**：`domain/` (业务实体和配置)

### 2️⃣ **需求方案文档交接** 
✅ **已有文档：**
- **需求方案**：`doc/架构/演进方向/需求方案.md`
- **技术架构**：`doc/架构/` (包含多版本架构图)
- **技术选型报告**：`doc/技术选型/` (各AI助手版本报告)
- **开发设计文档**：`doc/架构/演进方向/aug/` 
- **监控系统说明**：`监控系统使用说明.md`

❓ **需确认交接：**
- 最终确认的技术方案版本
- 业务需求变更历史
- 测试验收标准文档

### 3️⃣ **工具脚本交接**

✅ **已有脚本：**
1. **InfoGo数据处理**：`utils/infoGo_util.py`
2. **文档处理脚本**：`resource/folder_index_dev.py` 
3. **KG检索脚本**：`retrieve/kg_retrieve/kg_retrieve.py`
4. **实体链接脚本**：`retrieve/kg_retrieve/entity_link.py`

❓ **需确认交接：**
- TSM文档同步脚本
- 文档切分和向量化脚本  
- 数据预处理和清洗脚本
- 批量数据导入脚本

### 4️⃣ **系统账号密码交接**

❌ **需要获取：**

**LLM服务账号：**
- 大模型API Token: `config['LLM']['llm_headers']['Authorization']` (已加密)
- 员工号: `config['LLM']['llm_headers']['X-Emp-No']`

**ElasticSearch服务：**
- ES访问密钥: `config['Knowledge']['access_key']` (已加密)
- ES访问秘钥: `config['Knowledge']['access_secret']` (已加密) 
- ES服务地址: `https://kger.zte.com.cn/zte-dmt-dmp-kgbff/...`

**知识图谱服务：**
- KG服务地址: `https://kger.zte.com.cn/zte-dmt-dmp-kgbff/...`
- 知识空间: `product_multiversion`

**Milvus向量数据库：**
- 主机: `10.136.152.152:19530`
- 集合名: `productQA_1118`

**UAC统一认证：**
- App ID: `612791211526`
- 访问密钥和秘钥 (已加密存储)
- 验证服务地址: `https://uactest.zte.com.cn:5555/...`

**KMS密钥管理：**
- KMS域名: `https://kms-msp.test.zte.com.cn` 
- 密钥类型: `apoll:test:product_key`
- 数据密钥 (已加密)

**Kafka消息队列：**
- Broker地址: `10.54.158.92:9092, 10.54.158.93:9092, 10.54.158.94:9092`
- Topic: `acm-bk-app-nj`

### 5️⃣ **环境配置交接**

❓ **需要获取：**
- **配置解密密钥**：用于解密配置文件中的敏感信息
- **Apollo配置中心**：配置管理平台账号
- **开发/测试/生产环境**：各环境的配置差异说明
- **部署脚本**：Docker部署配置和启动脚本

### 6️⃣ **数据资源交接**

❓ **需要获取：**
- **测试数据集**：`doc/测试集/自建_业务方测试集auto_Qwen2_0723_dev.xlsx`
- **语料数据源**：TSM文档库访问权限
- **InfoGo系统**：数据同步账号
- **停用词表**：`resource/ZTE_stopwords.txt`
- **NLP模型**：`resource/spacy_ontonotes/`

### 7️⃣ **监控运维交接**

✅ **已有监控：**
- 召回监控系统 (监控ES、KG、Milvus召回效果)
- 日志系统 (存储在`logs/`和`resource/logs/`)
- 性能监控数据 (`monitor_data/`)

❓ **需要了解：**
- 告警机制和联系人
- 日志轮转策略
- 性能指标阈值
- 故障处理流程

### 8️⃣ **开发工具交接**

✅ **项目工具：**
- Python环境：requirements.txt已定义依赖
- PlantUML：架构图绘制工具 (`install_plantuml.bat`)
- 监控工具：`view_monitor.py`, `pkl_viewer.py`

## 🚨 **重要提醒**

1. **配置解密**：所有敏感配置都已加密，需要获取KMS解密密钥
2. **网络访问**：系统依赖多个内网服务，需要网络权限配置  
3. **数据权限**：需要TSM、InfoGo等系统的数据访问权限
4. **环境差异**：目前看到的是dev环境配置，需要了解其他环境差异

建议按照以上清单逐项确认交接，特别是加密配置的解密方法和各系统的访问权限。

---

_**User**_

简明写个需要他们交接的清单列表

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求简明的交接清单列表，不需要太详细的分析，直接列出核心的交接项目即可。我需要把之前的分析简化成清单格式。</details></think>

---

# ZTE产品技术问答系统交接清单

## 📄 **文档资料**
- [ ] 最终确认版需求方案文档
- [ ] 技术架构设计文档  
- [ ] 业务流程说明文档
- [ ] 测试验收标准
- [ ] 运维手册

## 🔐 **系统账号密码**
- [ ] **大模型服务**：API Token、员工号
- [ ] **ElasticSearch**：访问密钥和秘钥
- [ ] **知识图谱服务**：KG服务账号
- [ ] **Milvus向量库**：数据库连接信息
- [ ] **UAC统一认证**：App ID、访问密钥
- [ ] **KMS密钥管理**：解密密钥
- [ ] **Kafka消息队列**：连接配置
- [ ] **Apollo配置中心**：管理平台账号

## 🛠️ **工具脚本**
- [ ] TSM文档数据同步脚本
- [ ] 文档解析切分脚本  
- [ ] 数据预处理清洗脚本
- [ ] 向量化处理脚本
- [ ] 批量数据导入脚本
- [ ] 配置文件解密工具

## 🏗️ **环境配置**
- [ ] 开发环境配置文件
- [ ] 测试环境配置文件
- [ ] 生产环境配置文件
- [ ] Docker部署脚本
- [ ] 服务启动脚本
- [ ] 网络权限配置清单

## 📊 **数据资源**
- [ ] **数据源权限**：TSM文档库、InfoGo系统访问权限
- [ ] **测试数据集**：业务测试集和标准答案
- [ ] **训练语料**：历史问答数据
- [ ] **模型文件**：NLP模型、词向量模型
- [ ] **知识库备份**：ES索引、向量库数据

## 🔍 **监控运维**
- [ ] 监控系统访问权限
- [ ] 告警通知配置和联系人
- [ ] 日志查看权限和位置
- [ ] 性能指标阈值设置
- [ ] 故障处理SOP文档

## 👥 **团队对接**
- [ ] 技术负责人联系方式
- [ ] 运维支持人员联系方式  
- [ ] 业务方对接人联系方式
- [ ] 紧急联系人名单

## 📋 **交接确认**
- [ ] 系统功能演示
- [ ] 代码walkthrough
- [ ] 部署流程演示
- [ ] 监控系统使用培训
- [ ] 故障排查演示

---
**⚠️ 重点：所有配置文件中的敏感信息都已加密，务必获取KMS解密密钥和解密方法！**

---

_**User**_

使用表格表述

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求用表格形式来表述交接清单，我需要将之前的清单转换成表格格式，让内容更清晰、更易读。我可以按照不同的类别来组织表格，包含项目、具体内容、状态/备注等列。</details></think>

---

# ZTE产品技术问答系统交接清单

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **📄 文档资料** | 需求方案 | 最终确认版需求方案文档 | 🔴高 | 明确业务目标和功能边界 |
|  | 技术架构 | 系统架构设计文档、UML图 | 🔴高 | 理解系统整体设计 |
|  | 业务流程 | 问答流程、数据流向说明 | 🟡中 | 了解业务处理逻辑 |
|  | 测试标准 | 验收标准、测试用例 | 🟡中 | 质量控制依据 |
|  | 运维手册 | 部署、监控、故障处理SOP | 🔴高 | 系统运维必需 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **🔐 账号密码** | LLM服务 | API Token、员工号 | 🔴高 | 大模型调用必需 |
|  | ElasticSearch | 访问密钥、秘钥 | 🔴高 | 文档检索核心服务 |
|  | 知识图谱 | KG服务账号、空间权限 | 🔴高 | 关系检索服务 |
|  | Milvus向量库 | 连接地址、认证信息 | 🟡中 | 向量检索服务 |
|  | UAC统一认证 | App ID、访问密钥 | 🔴高 | 用户身份验证 |
|  | KMS密钥管理 | 配置解密密钥 | 🔴高 | **最关键！**解密所有配置 |
|  | Kafka消息队列 | Broker地址、Topic权限 | 🟡中 | 消息通信服务 |
|  | Apollo配置中心 | 管理平台账号 | 🟡中 | 配置管理平台 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **🛠️ 工具脚本** | TSM数据同步 | 文档自动同步脚本 | 🔴高 | 数据源同步工具 |
|  | 文档处理 | 解析、切分、预处理脚本 | 🔴高 | 数据预处理工具 |
|  | 向量化工具 | 文档向量化脚本 | 🟡中 | 向量生成工具 |
|  | 数据导入 | 批量数据导入脚本 | 🟡中 | 数据迁移工具 |
|  | 配置解密 | KMS配置解密工具 | 🔴高 | 敏感配置处理 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **🏗️ 环境配置** | 开发环境 | dev环境配置文件 | 🔴高 | 开发调试环境 |
|  | 测试环境 | test环境配置文件 | 🔴高 | 功能测试环境 |
|  | 生产环境 | prod环境配置文件 | 🔴高 | 生产运行环境 |
|  | 部署脚本 | Docker文件、启动脚本 | 🔴高 | 自动化部署 |
|  | 网络配置 | VPN、防火墙、域名配置 | 🟡中 | 网络访问权限 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **📊 数据资源** | 数据源权限 | TSM文档库、InfoGo系统权限 | 🔴高 | 数据获取权限 |
|  | 测试数据 | 业务测试集、标准答案 | 🟡中 | 功能验证数据 |
|  | 历史语料 | 问答历史数据、训练语料 | 🟡中 | 模型优化数据 |
|  | 模型文件 | NLP模型、词向量、停用词表 | 🟡中 | 算法模型资源 |
|  | 数据备份 | ES索引备份、向量库备份 | 🟢低 | 数据安全保障 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **🔍 监控运维** | 监控系统 | 监控平台账号、仪表盘配置 | 🔴高 | 系统健康监控 |
|  | 告警配置 | 告警规则、通知群组 | 🔴高 | 故障及时发现 |
|  | 日志系统 | 日志查看权限、日志位置 | 🟡中 | 问题排查工具 |
|  | 性能指标 | 关键指标阈值设置 | 🟡中 | 性能监控标准 |
|  | 故障处理 | 故障排查手册、应急预案 | 🔴高 | 故障快速恢复 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **👥 团队对接** | 技术负责人 | 架构师、开发负责人联系方式 | 🔴高 | 技术问题咨询 |
|  | 运维人员 | 运维工程师、DBA联系方式 | 🔴高 | 运维支持联系 |
|  | 业务对接人 | 产品经理、业务方联系人 | 🟡中 | 需求变更沟通 |
|  | 紧急联系人 | 7×24小时应急联系人 | 🔴高 | 紧急故障处理 |

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **📋 现场培训** | 系统演示 | 功能操作、接口调用演示 | 🔴高 | 熟悉系统功能 |
|  | 代码讲解 | 核心代码walkthrough | 🔴高 | 理解技术实现 |
|  | 部署实操 | 部署流程实际操作 | 🔴高 | 掌握部署技能 |
|  | 监控培训 | 监控系统使用培训 | 🟡中 | 运维技能培训 |
|  | 故障演练 | 常见故障排查演示 | 🟡中 | 应急处理能力 |

---

**🚨 特别提醒：**
- **🔐 KMS密钥管理**是最高优先级，没有解密密钥无法使用任何服务
- **📞 紧急联系人**务必要有，系统出问题需要快速响应  
- **📋 现场培训**建议安排2-3天集中交接

---

_**User**_

在一个表格中输出

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望把所有交接内容整合到一个表格中，而不是分开的多个表格。我需要把所有的交接项目都放在一个大表格里，保持清晰的分类和优先级标识。</details></think>

---

# ZTE产品技术问答系统交接清单

| **类别** | **交接项目** | **具体内容** | **优先级** | **备注** |
|---------|-------------|------------|-----------|----------|
| **📄 文档资料** | 需求方案 | 最终确认版需求方案文档 | 🔴高 | 明确业务目标和功能边界 |
| **📄 文档资料** | 技术架构 | 系统架构设计文档、UML图 | 🔴高 | 理解系统整体设计 |
| **📄 文档资料** | 业务流程 | 问答流程、数据流向说明 | 🟡中 | 了解业务处理逻辑 |
| **📄 文档资料** | 测试标准 | 验收标准、测试用例 | 🟡中 | 质量控制依据 |
| **📄 文档资料** | 运维手册 | 部署、监控、故障处理SOP | 🔴高 | 系统运维必需 |
| **🔐 账号密码** | KMS密钥管理 | **配置解密密钥** | 🔴高 | **最关键！**解密所有配置 |
| **🔐 账号密码** | LLM服务 | API Token、员工号 | 🔴高 | 大模型调用必需 |
| **🔐 账号密码** | ElasticSearch | 访问密钥、秘钥 | 🔴高 | 文档检索核心服务 |
| **🔐 账号密码** | 知识图谱 | KG服务账号、空间权限 | 🔴高 | 关系检索服务 |
| **🔐 账号密码** | UAC统一认证 | App ID、访问密钥 | 🔴高 | 用户身份验证 |
| **🔐 账号密码** | Milvus向量库 | 连接地址、认证信息 | 🟡中 | 向量检索服务 |
| **🔐 账号密码** | Kafka消息队列 | Broker地址、Topic权限 | 🟡中 | 消息通信服务 |
| **🔐 账号密码** | Apollo配置中心 | 管理平台账号 | 🟡中 | 配置管理平台 |
| **🛠️ 工具脚本** | TSM数据同步 | 文档自动同步脚本 | 🔴高 | 数据源同步工具 |
| **🛠️ 工具脚本** | 文档处理 | 解析、切分、预处理脚本 | 🔴高 | 数据预处理工具 |
| **🛠️ 工具脚本** | 配置解密 | KMS配置解密工具 | 🔴高 | 敏感配置处理 |
| **🛠️ 工具脚本** | 向量化工具 | 文档向量化脚本 | 🟡中 | 向量生成工具 |
| **🛠️ 工具脚本** | 数据导入 | 批量数据导入脚本 | 🟡中 | 数据迁移工具 |
| **🏗️ 环境配置** | 开发环境 | dev环境配置文件 | 🔴高 | 开发调试环境 |
| **🏗️ 环境配置** | 测试环境 | test环境配置文件 | 🔴高 | 功能测试环境 |
| **🏗️ 环境配置** | 生产环境 | prod环境配置文件 | 🔴高 | 生产运行环境 |
| **🏗️ 环境配置** | 部署脚本 | Docker文件、启动脚本 | 🔴高 | 自动化部署 |
| **🏗️ 环境配置** | 网络配置 | VPN、防火墙、域名配置 | 🟡中 | 网络访问权限 |
| **📊 数据资源** | 数据源权限 | TSM文档库、InfoGo系统权限 | 🔴高 | 数据获取权限 |
| **📊 数据资源** | 测试数据 | 业务测试集、标准答案 | 🟡中 | 功能验证数据 |
| **📊 数据资源** | 历史语料 | 问答历史数据、训练语料 | 🟡中 | 模型优化数据 |
| **📊 数据资源** | 模型文件 | NLP模型、词向量、停用词表 | 🟡中 | 算法模型资源 |
| **📊 数据资源** | 数据备份 | ES索引备份、向量库备份 | 🟢低 | 数据安全保障 |
| **🔍 监控运维** | 监控系统 | 监控平台账号、仪表盘配置 | 🔴高 | 系统健康监控 |
| **🔍 监控运维** | 告警配置 | 告警规则、通知群组 | 🔴高 | 故障及时发现 |
| **🔍 监控运维** | 故障处理 | 故障排查手册、应急预案 | 🔴高 | 故障快速恢复 |
| **🔍 监控运维** | 日志系统 | 日志查看权限、日志位置 | 🟡中 | 问题排查工具 |
| **🔍 监控运维** | 性能指标 | 关键指标阈值设置 | 🟡中 | 性能监控标准 |
| **👥 团队对接** | 技术负责人 | 架构师、开发负责人联系方式 | 🔴高 | 技术问题咨询 |
| **👥 团队对接** | 运维人员 | 运维工程师、DBA联系方式 | 🔴高 | 运维支持联系 |
| **👥 团队对接** | 紧急联系人 | 7×24小时应急联系人 | 🔴高 | 紧急故障处理 |
| **👥 团队对接** | 业务对接人 | 产品经理、业务方联系人 | 🟡中 | 需求变更沟通 |
| **📋 现场培训** | 系统演示 | 功能操作、接口调用演示 | 🔴高 | 熟悉系统功能 |
| **📋 现场培训** | 代码讲解 | 核心代码walkthrough | 🔴高 | 理解技术实现 |
| **📋 现场培训** | 部署实操 | 部署流程实际操作 | 🔴高 | 掌握部署技能 |
| **📋 现场培训** | 监控培训 | 监控系统使用培训 | 🟡中 | 运维技能培训 |
| **📋 现场培训** | 故障演练 | 常见故障排查演示 | 🟡中 | 应急处理能力 |

---

**🚨 特别提醒：**
- **🔐 KMS密钥管理**是最高优先级，没有解密密钥无法使用任何服务
- **📞 紧急联系人**务必要有，系统出问题需要快速响应  
- **📋 现场培训**建议安排2-3天集中交接
- **总计41个交接项**：🔴高优先级22项，🟡中优先级18项，🟢低优先级1项

---

_**User**_

不要花里花哨

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求不要花里花哨，意思是不要用颜色标记、emoji等装饰，要简洁明了的表格。我重新整理一个简洁的表格。</details></think>

---

# ZTE产品技术问答系统交接清单

| 类别 | 交接项目 | 具体内容 | 优先级 | 备注 |
|------|----------|----------|--------|------|
| 文档资料 | 需求方案 | 最终确认版需求方案文档 | 高 | 明确业务目标和功能边界 |
| 文档资料 | 技术架构 | 系统架构设计文档、UML图 | 高 | 理解系统整体设计 |
| 文档资料 | 业务流程 | 问答流程、数据流向说明 | 中 | 了解业务处理逻辑 |
| 文档资料 | 测试标准 | 验收标准、测试用例 | 中 | 质量控制依据 |
| 文档资料 | 运维手册 | 部署、监控、故障处理SOP | 高 | 系统运维必需 |
| 账号密码 | KMS密钥管理 | 配置解密密钥 | 高 | 最关键！解密所有配置 |
| 账号密码 | LLM服务 | API Token、员工号 | 高 | 大模型调用必需 |
| 账号密码 | ElasticSearch | 访问密钥、秘钥 | 高 | 文档检索核心服务 |
| 账号密码 | 知识图谱 | KG服务账号、空间权限 | 高 | 关系检索服务 |
| 账号密码 | UAC统一认证 | App ID、访问密钥 | 高 | 用户身份验证 |
| 账号密码 | Milvus向量库 | 连接地址、认证信息 | 中 | 向量检索服务 |
| 账号密码 | Kafka消息队列 | Broker地址、Topic权限 | 中 | 消息通信服务 |
| 账号密码 | Apollo配置中心 | 管理平台账号 | 中 | 配置管理平台 |
| 工具脚本 | TSM数据同步 | 文档自动同步脚本 | 高 | 数据源同步工具 |
| 工具脚本 | 文档处理 | 解析、切分、预处理脚本 | 高 | 数据预处理工具 |
| 工具脚本 | 配置解密 | KMS配置解密工具 | 高 | 敏感配置处理 |
| 工具脚本 | 向量化工具 | 文档向量化脚本 | 中 | 向量生成工具 |
| 工具脚本 | 数据导入 | 批量数据导入脚本 | 中 | 数据迁移工具 |
| 环境配置 | 开发环境 | dev环境配置文件 | 高 | 开发调试环境 |
| 环境配置 | 测试环境 | test环境配置文件 | 高 | 功能测试环境 |
| 环境配置 | 生产环境 | prod环境配置文件 | 高 | 生产运行环境 |
| 环境配置 | 部署脚本 | Docker文件、启动脚本 | 高 | 自动化部署 |
| 环境配置 | 网络配置 | VPN、防火墙、域名配置 | 中 | 网络访问权限 |
| 数据资源 | 数据源权限 | TSM文档库、InfoGo系统权限 | 高 | 数据获取权限 |
| 数据资源 | 测试数据 | 业务测试集、标准答案 | 中 | 功能验证数据 |
| 数据资源 | 历史语料 | 问答历史数据、训练语料 | 中 | 模型优化数据 |
| 数据资源 | 模型文件 | NLP模型、词向量、停用词表 | 中 | 算法模型资源 |
| 数据资源 | 数据备份 | ES索引备份、向量库备份 | 低 | 数据安全保障 |
| 监控运维 | 监控系统 | 监控平台账号、仪表盘配置 | 高 | 系统健康监控 |
| 监控运维 | 告警配置 | 告警规则、通知群组 | 高 | 故障及时发现 |
| 监控运维 | 故障处理 | 故障排查手册、应急预案 | 高 | 故障快速恢复 |
| 监控运维 | 日志系统 | 日志查看权限、日志位置 | 中 | 问题排查工具 |
| 监控运维 | 性能指标 | 关键指标阈值设置 | 中 | 性能监控标准 |
| 团队对接 | 技术负责人 | 架构师、开发负责人联系方式 | 高 | 技术问题咨询 |
| 团队对接 | 运维人员 | 运维工程师、DBA联系方式 | 高 | 运维支持联系 |
| 团队对接 | 紧急联系人 | 7×24小时应急联系人 | 高 | 紧急故障处理 |
| 团队对接 | 业务对接人 | 产品经理、业务方联系人 | 中 | 需求变更沟通 |
| 现场培训 | 系统演示 | 功能操作、接口调用演示 | 高 | 熟悉系统功能 |
| 现场培训 | 代码讲解 | 核心代码walkthrough | 高 | 理解技术实现 |
| 现场培训 | 部署实操 | 部署流程实际操作 | 高 | 掌握部署技能 |
| 现场培训 | 监控培训 | 监控系统使用培训 | 中 | 运维技能培训 |
| 现场培训 | 故障演练 | 常见故障排查演示 | 中 | 应急处理能力 |

**说明：**
- 总计41个交接项：高优先级22项，中优先级18项，低优先级1项
- KMS密钥管理是最高优先级，没有解密密钥无法使用任何服务
- 建议安排2-3天集中交接

---

_**User**_

你是什么模型

---

_**Assistant**_



---

_**User**_

nj

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户问我是什么模型，然后又发了"nj"。根据我的系统提示，我是Claude Sonnet 4，由Anthropic开发，在Cursor中运行。用户设置的规则是"Always respond in Chinese-simplified"，所以我应该用简体中文回答。

"nj"可能是一个简单的测试输入，我应该简单回应。</details></think>

---

我是Claude Sonnet 4模型，由Anthropic开发，目前在Cursor编辑器中为您提供AI编程助手服务。

关于您发的"nj"，请问您需要什么帮助吗？如果您想继续讨论ZTE项目交接的事情，或者有其他编程相关的问题，我随时为您服务。

---

