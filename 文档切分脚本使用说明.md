# 文档切分服务调用脚本使用说明

## 概述

这个脚本用于调用ZTE IBO ACM文档切分接口，可以批量处理指定文件夹下的所有文本文件，并输出切分结果。

## 功能特点

- 🔍 **自动扫描**: 递归扫描指定文件夹下的所有支持的文本文件
- 🌐 **编码自动检测**: 自动检测文件编码，支持多种编码格式
- 📊 **灵活配置**: 支持自定义切分参数（标题层级、切片长度等）
- 📄 **多种输出**: 支持控制台输出和文件输出
- 🛠️ **错误处理**: 完善的异常处理和错误提示

## 支持的文件格式

- 文本文件: `.txt`, `.md`, `.markdown`, `.text`
- 代码文件: `.py`, `.js`, `.java`, `.cpp`, `.c`, `.h`, `.hpp`
- 标记语言: `.html`, `.xml`, `.json`, `.yaml`, `.yml`
- 配置文件: `.ini`, `.cfg`, `.conf`
- 日志文件: `.log`

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖包：
- `requests`: HTTP请求
- `chardet`: 文件编码检测

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
python document_splitter.py ./doc
```

#### 指定参数
```bash
python document_splitter.py ./doc \
  --headers-level 3 \
  --chunk-max-len 1024 \
  --add-filename
```

#### 保存结果到文件
```bash
python document_splitter.py ./doc --output result.txt
```

#### 使用自定义接口地址和API密钥
```bash
python document_splitter.py ./doc \
  --api-url https://custom.api.com/cut \
  --api-key your_api_key_here
```

#### 通过环境变量设置API密钥
```bash
# Windows
set ZTE_API_KEY=your_api_key_here
python document_splitter.py ./doc

# Linux/Mac
export ZTE_API_KEY=your_api_key_here
python document_splitter.py ./doc
```

### 2. 参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|-----|------|------|--------|------|
| `folder_path` | string | 是 | - | 要处理的文件夹路径 |
| `--api-url` | string | 否 | `https://icosg.test.zte.com.cn/zte-ibo-acm-tools/cut` | 文档切分接口地址 |
| `--api-key` | string | 否 | - | API密钥（也可通过环境变量设置） |
| `--headers-level` | int | 否 | 2 | 切分标题层级 (1-6) |
| `--chunk-max-len` | int | 否 | 2048 | 最大切片长度 (100-2048) |
| `--add-filename` | bool | 否 | False | 在切片中添加文件名 |
| `--output` | string | 否 | - | 输出文件路径 |

### 3. 查看帮助
```bash
python document_splitter.py --help
```

## 编程方式使用

```python
from document_splitter import DocumentSplitter
import os

# 方法1：直接传入API密钥
splitter = DocumentSplitter(api_key="your_api_key_here")

# 方法2：通过环境变量
os.environ['ZTE_API_KEY'] = "your_api_key_here"
splitter = DocumentSplitter()

# 扫描文件夹
file_texts = splitter.scan_folder("./doc")

# 调用接口
result = splitter.call_split_api(
    file_texts=file_texts,
    headers_level=2,
    chunk_max_len=800,
    add_filename=True
)

# 输出结果
splitter.format_output(result)
```

## 快速开始

### Windows用户
1. 双击运行 `run_document_splitter.bat`
2. 按提示操作

### Linux/Mac用户
1. 运行示例：
   ```bash
   python document_splitter_example.py
   ```

2. 处理文档：
   ```bash
   python document_splitter.py ./your_folder
   ```

## 输出格式

脚本会输出以下信息：

1. **处理概览**：
   - 返回码和状态信息
   - 处理的文档数量
   - 生成的切片总数

2. **详细结果**：
   - 每个文档的切分结果
   - 每个切片的内容预览
   - 切片长度统计

## 示例输出

```
============================================================
文档切分结果
============================================================
返回码: 0000
返回信息: 成功

切分结果概览:
- 处理文档数量: 2
- 生成切片总数: 5

详细切分结果:
----------------------------------------

文档 1 的切分结果 (共 3 个切片):
==================================================

切片 1:
--------------------
# 技术文档标题

这是文档的第一部分内容...
(切片长度: 156 字符)

切片 2:
--------------------
## 第二章节

这是文档的第二部分内容...
(切片长度: 284 字符)
```

## 注意事项

1. **网络连接**: 确保网络能访问接口地址
2. **文件编码**: 脚本会自动检测文件编码，但建议使用UTF-8编码
3. **文件大小**: 大文件可能需要较长处理时间
4. **接口限制**: 注意接口的并发限制和请求频率限制

## 常见问题

### Q: 提示"无法解析导入chardet"
A: 运行 `pip install chardet` 安装依赖包

### Q: 接口调用失败，提示"No API key found"
A: 需要设置API密钥，可以通过 `--api-key` 参数或环境变量 `ZTE_API_KEY` 设置

### Q: 接口调用失败，HTTP状态码401
A: API密钥无效或已过期，请检查密钥是否正确

### Q: 接口调用失败，其他网络错误
A: 检查网络连接和接口地址是否正确

### Q: 文件读取失败
A: 检查文件路径和权限，确保文件可读取

### Q: 输出结果为空
A: 检查文件夹下是否有支持的文本文件格式

## 技术支持

如有问题或建议，请联系开发团队。
