"""
Markdown文本切分工具

"""

import base64
import re
import os
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Optional, Any
from bs4 import BeautifulSoup
from langchain.text_splitter import MarkdownHeaderTextSplitter, RecursiveCharacterTextSplitter


class ImageProcessor:
    """图片处理器，负责base64图片的解码和本地存储"""
    
    def __init__(self, pic_temp_path: str):
        """
        初始化图片处理器
        
        Args:
            pic_temp_path: 图片临时存储路径
        """
        self.pic_temp_path = pic_temp_path
    
    def save_image_to_local(self, image_data: bytes, filename: str) -> str:
        """
        将图片数据保存到本地
        
        Args:
            image_data: 图片的二进制数据
            filename: 文件名
            
        Returns:
            保存后的文件路径
            
        Raises:
            Exception: 图片保存失败时抛出异常
        """
        try:
            # 创建唯一标识符
            u_id = str(uuid.uuid4())
            # 创建目标文件夹路径
            file_path = f'{self.pic_temp_path}/{u_id}/{filename}'
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 保存图片数据到指定文件
            with open(file_path, 'wb') as file:
                file.write(image_data)

            return file_path
        except Exception as e:
            raise Exception(f"图片保存到本地失败: {e}")


class TextProcessor:
    """文本处理器，负责文本的预处理和后处理"""
    
    @staticmethod
    def replace_multiple_spaces(text: str) -> str:
        """
        去除连续空格，将多个空格替换为单个空格
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本
        """
        pattern = r'[ ]+'
        return re.sub(pattern, ' ', text)
    
    @staticmethod
    def replace_consecutive_hyphens(text: str) -> str:
        """
        去除表格中连续的横线，将多个横线替换为单个横线
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本
        """
        return re.sub(r'-+', '-', text)
    
    @staticmethod
    def process_punctuation(chunks: List[str]) -> List[str]:
        """
        处理标点符号，确保句号正确分割
        
        Args:
            chunks: 文本块列表
            
        Returns:
            处理后的文本块列表
        """
        for i in range(1, len(chunks)):
            if chunks[i].startswith("。"):
                chunks[i - 1] += "。"
                chunks[i] = chunks[i][1:]
        return chunks


class TableProcessor:
    """表格处理器，负责HTML表格转Markdown和表格切分"""
    
    @staticmethod
    def convert_html_tables_to_md(text: str) -> str:
        """
        将HTML表格转换为Markdown格式
        
        Args:
            text: 包含HTML表格的文本
            
        Returns:
            转换后的文本
        """
        html_tables = re.findall(r'(<table.*?>.*?</table>)', text, re.DOTALL)

        for html_table in html_tables:
            md_table = TableProcessor._html_table_to_md(html_table)
            text = text.replace(html_table, md_table)

        return text
    
    @staticmethod
    def _html_table_to_md(html_table: str) -> str:
        """
        将单个HTML表格转换为Markdown格式
        
        Args:
            html_table: HTML表格字符串
            
        Returns:
            Markdown格式的表格
        """
        soup = BeautifulSoup(html_table, 'html.parser')
        rows = soup.find_all('tr')

        md_table = []
        for row in rows:
            columns = row.find_all(['td', 'th'])
            md_row = '| ' + ' | '.join(col.get_text(strip=True) for col in columns) + ' |'
            md_table.append(md_row)

        # 插入表头分隔行
        if rows:
            md_table.insert(1, '| ' + ' | '.join(['---'] * len(columns)) + ' |')

        return '\n'.join(md_table)
    
    @staticmethod
    def check_table(md_content: str) -> bool:
        """
        检查文本是否包含表格
        
        Args:
            md_content: Markdown内容
            
        Returns:
            是否包含表格
        """
        table_pattern = r'\|.*\|'
        return bool(re.search(table_pattern, md_content))
    
    @staticmethod
    def judge_first_line(line: str) -> bool:
        """
        判断第一行是否为表格行
        
        Args:
            line: 第一行内容
            
        Returns:
            是否为表格行
        """
        return line.count('|') >= 2
    
    @staticmethod
    def table_text_split_all(text: str) -> List[Tuple[str, str]]:
        """
        将文本和表格分离，给每一行打标签
        
        Args:
            text: 输入文本
            
        Returns:
            标签化的内容列表，每个元素为(标签, 内容)
        """
        lines = text.split("\n")
        cont_md = []
        
        for i in range(len(lines)):
            line = lines[i].strip()
            # 每行字符串中包含"|"数量为2个及以上，则认为是表格
            if line.count('|') >= 2:
                # 检查前面是否有文本行，如果有则将其与表格合并为一个整体
                if i > 0 and cont_md and cont_md[-1][1] == "text":
                    # 将前面的文本行标记为table，这样会被合并到表格块中
                    cont_md.append((len(cont_md), "table", lines[i - 1].strip()))
                cont_md.append((len(cont_md), "table", line))
            else:
                cont_md.append((len(cont_md), "text", line))

        return TableProcessor._contract_content(cont_md)
    
    @staticmethod
    def table_text_split_all_excel(text: str) -> List[Tuple[str, str]]:
        """
        Excel格式的表格文本分离
        
        Args:
            text: 输入文本
            
        Returns:
            标签化的内容列表
        """
        lines = text.split("\n")
        cont_md = []
        
        for i in range(len(lines)):
            line = lines[i].strip()
            if line.count('|') >= 2:
                cont_md.append((len(cont_md), "table", line))
            else:
                cont_md.append((len(cont_md), "text", line))

        return TableProcessor._contract_content(cont_md)
    
    @staticmethod
    def _contract_content(md: List[Tuple[int, str, str]]) -> List[Tuple[str, str]]:
        """
        整理标签化的内容，合并连续的相同类型内容
        
        Args:
            md: 标签化的内容列表
            
        Returns:
            整理后的内容列表
        """
        result = []
        temp_table = ""
        temp_table_i = 0
        
        for j in range(len(md)):
            table_i, tag, table_line = md[j]
            
            if j == 0:
                temp_table_i = table_i
                tag_i = tag
                temp_table += table_line + "\n"
            elif temp_table_i + 1 == table_i and tag_i == tag:
                if tag == "table":
                    # 对于表格内容，直接合并，不进行特殊处理
                    temp_table_i = table_i
                    temp_table += table_line + "\n"
                    if j == len(md) - 1:
                        result.append((tag, temp_table))
                else:
                    temp_table_i = table_i
                    temp_table += table_line + "\n"
                    if j == len(md) - 1:
                        result.append((tag, temp_table))
            else:
                result.append((tag_i, temp_table))
                tag_i = tag
                temp_table_i = table_i
                temp_table = table_line + "\n"
                if j == len(md) - 1:
                    result.append((tag, temp_table))

        return result


class HeaderProcessor:
    """标题处理器，负责Markdown标题的处理和验证"""
    
    @staticmethod
    def header_supplement(header_data: Dict[str, str]) -> Dict[str, str]:
        """
        为标题数据补充Markdown标记
        
        Args:
            header_data: 标题数据字典
            
        Returns:
            补充后的标题数据
        """
        header_map = {
            'Header 1': '# ',
            'Header 2': '## ',
            'Header 3': '### ',
            'Header 4': '#### ',
            'Header 5': '##### ',
            'Header 6': '###### '
        }

        for key, value in header_data.items():
            if key in header_map:
                header_data[key] = header_map[key] + value

        return header_data
    
    @staticmethod
    def check_header(text: str) -> str:
        """
        检查并修复标题格式
        
        Args:
            text: 输入文本
            
        Returns:
            修复后的文本
        """
        lines = text.splitlines(keepends=True)
        result = []
        previous_line = None

        for line in lines:
            if not line.strip():
                result.append(line)
                continue

            if previous_line is not None:
                current_hash_count = HeaderProcessor._get_hash_count(line)
                previous_hash_count = HeaderProcessor._get_hash_count(previous_line)

                if current_hash_count > 0 and current_hash_count == previous_hash_count:
                    result.append("-\n")

            result.append(line)
            previous_line = line

        return ''.join(result)
    
    @staticmethod
    def _get_hash_count(line: str) -> int:
        """
        获取行首连续的#号数量
        
        Args:
            line: 输入行
            
        Returns:
            #号数量
        """
        stripped_line = line.strip()
        if stripped_line.startswith('#'):
            return len(stripped_line.split(' ')[0])
        return 0


class ChunkProcessor:
    """文本块处理器，负责文本块的切分和大小控制"""
    
    def __init__(self, max_chunk_len: int = 800):
        """
        初始化文本块处理器
        
        Args:
            max_chunk_len: 最大块长度
        """
        self.max_chunk_len = min(max_chunk_len, 2048)  # 限制最大长度
    
    def control_deal_content(self, chunk_data: Tuple[Dict[str, str], str], 
                           supplement_len: int, file_type: str) -> List[Tuple[str, str]]:
        """
        控制文本块的处理逻辑
        
        Args:
            chunk_data: 块数据，包含元数据和内容
            supplement_len: 补充长度
            file_type: 文件类型
            
        Returns:
            处理后的块列表
        """
        chunk = []
        chunk_len = max(1, self.max_chunk_len - supplement_len)
        text = chunk_data[1]
        
        # 将HTML格式的表格转换为MD格式
        text = TableProcessor.convert_html_tables_to_md(text)
        
        # 判断是否有表格
        if TableProcessor.check_table(text):
            return self._process_with_table(text, chunk_len, file_type)
        else:
            return self._process_without_table(text, chunk_len)
    
    def _process_with_table(self, text: str, chunk_len: int, file_type: str) -> List[Tuple[str, str]]:
        """
        处理包含表格的文本
        
        Args:
            text: 输入文本
            chunk_len: 块长度
            file_type: 文件类型
            
        Returns:
            处理后的块列表
        """
        chunk = []
        
        # 去除多余的空格和横线
        text = TextProcessor.replace_consecutive_hyphens(text)
        text = TextProcessor.replace_multiple_spaces(text)

        # 将文本和表格进行切分开来
        if file_type == '.xlsx':
            res_text = TableProcessor.table_text_split_all_excel(text)
        else:
            res_text = TableProcessor.table_text_split_all(text)

        # 切分表格
        for tag, data in res_text:
            if tag == "table":
                chunk.extend(self._process_table_data(data, chunk_len, file_type))
            else:
                chunk.extend(self._process_text_data(data, chunk_len))

        return chunk
    
    def _process_without_table(self, text: str, chunk_len: int) -> List[Tuple[str, str]]:
        """
        处理不包含表格的文本
        
        Args:
            text: 输入文本
            chunk_len: 块长度
            
        Returns:
            处理后的块列表
        """
        if len(text) > chunk_len:
            text_splitter = RecursiveCharacterTextSplitter(
                separators=["。", "."], 
                chunk_size=chunk_len, 
                chunk_overlap=0
            )
            split_contents = text_splitter.split_text(text)
            split_contents = TextProcessor.process_punctuation(split_contents)
            return [("text", txt) for txt in split_contents]
        else:
            return [("text", text)]
    
    def _process_table_data(self, data: str, chunk_len: int, file_type: str) -> List[Tuple[str, str]]:
        """
        处理表格数据
        
        Args:
            data: 表格数据
            chunk_len: 块长度
            file_type: 文件类型
            
        Returns:
            处理后的块列表
        """
        # 首先检查整个表格内容是否超过最大长度
        if len(data) <= chunk_len:
            # 如果整个表格内容不超过最大长度，直接作为一个块返回
            return [("table", data)]
        
        # 只有在内容超过最大长度时才进行切分
        chunk = []
        lines = data.split("\n")
        header_flag = TableProcessor.judge_first_line(lines[0])
        
        if len(lines) < 3:
            chunk.append(("text", data))
            return chunk
            
        if file_type == '.xlsx' or header_flag:
            table_header = lines[0] + '\n' + lines[1]
            table_header_index = 1
        else:
            table_header = lines[0] + '\n' + lines[1] + '\n' + lines[2]
            table_header_index = 2

        table_content = table_header

        for i, line in enumerate(lines):
            if i <= table_header_index:
                continue
            elif i > table_header_index and i < len(lines) - 1:
                if len(line) > chunk_len:
                    chunk.append(("table", table_header + '\n' + line))
                elif len(table_content + '\n' + line) < chunk_len:
                    table_content = table_content + '\n' + line
                else:
                    chunk.append(("table", table_content))
                    table_content = table_header + '\n' + line
            elif i == len(lines) - 1:
                if len(table_content + '\n' + line) < chunk_len:
                    table_content = table_content + '\n' + line
                    chunk.append(("table", table_content))
                else:
                    chunk.append(("table", table_content))
                    chunk.append(("table", table_header + '\n' + line))

        return chunk
    
    def _process_text_data(self, data: str, chunk_len: int) -> List[Tuple[str, str]]:
        """
        处理文本数据
        
        Args:
            data: 文本数据
            chunk_len: 块长度
            
        Returns:
            处理后的块列表
        """
        if len(data) > chunk_len:
            text_splitter = RecursiveCharacterTextSplitter(
                separators=["。", "."], 
                chunk_size=chunk_len, 
                chunk_overlap=0
            )
            split_contents = text_splitter.split_text(data)
            split_contents = TextProcessor.process_punctuation(split_contents)
            return [("text", txt) for txt in split_contents]
        else:
            return [("text", data)]


class MarkdownSplitter:
    """Markdown文本切分器，主要的切分逻辑"""
    
    def __init__(self, pic_temp_path: str = "./temp_images"):
        """
        初始化Markdown切分器
        
        Args:
            pic_temp_path: 图片临时存储路径
        """
        self.image_processor = ImageProcessor(pic_temp_path)
        self.chunk_processor = ChunkProcessor()
    
    def split_file(self, file_path: str, headers_level: int = 6, 
                  max_chunk_len: int = 800) -> List[Dict[str, Any]]:
        """
        切分Markdown文件
        
        Args:
            file_path: 文件路径
            headers_level: 标题层级
            max_chunk_len: 最大块长度
            
        Returns:
            切分后的块列表
            
        Raises:
            FileNotFoundError: 文件未找到
            RuntimeError: 读取文件时出错
        """
        # 验证参数
        if max_chunk_len > 2048:
            max_chunk_len = 2048
        
        # 读取文件
        text = self._read_file(file_path)
        
        # 处理图片
        text, image_data_dict = self._process_images(text)
        
        # 切分文本
        chunks = self._split_text_by_headers(text, headers_level, max_chunk_len, file_path)
        
        # 处理结果块
        return self._process_result_chunks(chunks, image_data_dict)
    
    def _read_file(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
            
        Raises:
            FileNotFoundError: 文件未找到
            RuntimeError: 读取文件时出错
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"文件未找到：{file_path}")
        except Exception as e:
            raise RuntimeError(f"读取文件时出错：{e}")
    
    def _process_images(self, text: str) -> Tuple[str, Dict[str, str]]:
        """
        处理文本中的图片
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本和图片数据字典
        """
        pattern = r'------Image Start ------\s*(.*?)\s*------Image End ------'
        matches = re.findall(pattern, text, re.DOTALL)

        image_data_dict = {}
        for index, match in enumerate(matches):
            # 提取图片格式
            match_format = re.match(r'(.*?)；', match)
            pic_format = match_format.group(1) if match_format else '.png'
            pic_base64 = re.sub(r'^.*?；', '', match)

            image_data = base64.b64decode(pic_base64)
            pic_file_path = self.image_processor.save_image_to_local(
                image_data, f"{index}{pic_format}"
            )
            image_data_dict[f"-image{index}-"] = pic_file_path
            # 替换原始标记和内容
            text = re.sub(pattern, f"-image{index}-", text, count=1)

        return text, image_data_dict
    
    def _split_text_by_headers(self, text: str, headers_level: int, 
                              max_chunk_len: int, file_path: str) -> List[Tuple[Dict[str, str], str]]:
        """
        按标题切分文本
        
        Args:
            text: 输入文本
            headers_level: 标题层级
            max_chunk_len: 最大块长度
            file_path: 文件路径
            
        Returns:
            切分后的内容列表
        """
        headers_to_split_on_all = [
            ("#", "Header 1"),
            ("##", "Header 2"),
            ("###", "Header 3"),
            ("####", "Header 4"),
            ("#####", "Header 5"),
            ("######", "Header 6"),
        ]

        headers_to_split_on = headers_to_split_on_all[:headers_level]

        markdown_splitter = MarkdownHeaderTextSplitter(
            headers_to_split_on=headers_to_split_on
        )

        doc_string = HeaderProcessor.check_header(text)
        md_header_splits = markdown_splitter.split_text(doc_string)
        contents = []

        for i in md_header_splits:
            if i.page_content == '***':
                continue
            
            header_data_pri = i.metadata
            header_data = HeaderProcessor.header_supplement(header_data_pri)
            bunk_content = i.page_content
            
            contents.append(
                (header_data, TextProcessor.replace_multiple_spaces(bunk_content))
            )

        return self._process_contents(contents, file_path, max_chunk_len)
    
    def _process_contents(self, contents: List[Tuple[Dict[str, str], str]], 
                         file_path: str, max_chunk_len: int) -> List[str]:
        """
        处理内容列表
        
        Args:
            contents: 内容列表
            file_path: 文件路径
            max_chunk_len: 最大块长度
            
        Returns:
            处理后的文本块列表
        """
        base = os.path.basename(file_path)
        full_text_header, file_type = os.path.splitext(base)
        
        temp_chunks = []
        title = ""

        for data in contents:
            # 处理标题
            header_data = list(data[0].values())
            if len(header_data) > 0:
                title = header_data[-1]

            full_title = '\n'.join(header_data)

            # 计算文件名和标题长度
            supplement_len = len(full_text_header + full_title) if full_text_header else len(full_title)
            if supplement_len > max_chunk_len:
                supplement_len = len(full_text_header) if full_text_header else 0

            # 控制块处理
            self.chunk_processor.max_chunk_len = max_chunk_len
            chunks = self.chunk_processor.control_deal_content(data, supplement_len, file_type)

            for chunk in chunks:
                new_value = f"{full_text_header}\n{full_title}\n{chunk[1]}" if full_text_header else f"{full_title}\n{chunk[1]}"
                temp_chunks.append((chunk[0], new_value))

        return [value for _, value in temp_chunks]
    
    def _process_result_chunks(self, chunks: List[str], 
                             image_data_dict: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        处理结果块，添加图片信息
        
        Args:
            chunks: 文本块列表
            image_data_dict: 图片数据字典
            
        Returns:
            最终的结果块列表
        """
        chunk_dict_list = []
        for chunk in chunks:
            chunk_dict = self._process_res_chunk(chunk, image_data_dict)
            chunk_dict_list.append(chunk_dict)

        return chunk_dict_list
    
    def _process_res_chunk(self, input_string: str, 
                          image_data_dict: Dict[str, str]) -> Dict[str, Any]:
        """
        处理单个结果块
        
        Args:
            input_string: 输入字符串
            image_data_dict: 图片数据字典
            
        Returns:
            处理后的块字典
        """
        pict_list = []
        pattern = r'(-image\d+-)'
        matches = re.finditer(pattern, input_string)

        if not any(matches):
            return self._assemble_res_chunk(input_string, pict_list)

        matches = re.finditer(pattern, input_string)

        for match in matches:
            image_string = match.group(1)
            position = match.start()
            pict_list.append({
                "index": position, 
                "local_path": image_data_dict[image_string], 
                "owner": "content"
            })

        # 自定义替换函数
        def replace_with_dashes(match):
            return ' ' * len(match.group(0))

        # 使用 re.sub 进行替换
        pri_string = re.sub(pattern, replace_with_dashes, input_string)
        return self._assemble_res_chunk(pri_string, pict_list)
    
    def _assemble_res_chunk(self, chunk_str: str, pict_dist: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        组装结果块
        
        Args:
            chunk_str: 块字符串
            pict_dist: 图片分布信息
            
        Returns:
            组装后的块字典
        """
        return {
            'content': chunk_str,
            'pict': pict_dist,
            'supplement': '',
            'metadata': {}
        }


# 全局变量，用于兼容旧代码
pic_temp_path = "./temp_images"

# 线程安全的打印锁
_print_lock = threading.Lock()

def _thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with _print_lock:
        print(*args, **kwargs)


def _process_single_file(file_info: Dict[str, str], headers_level: int, max_chunk_len: int, 
                        output_folder: str, file_index: int, total_files: int) -> Dict[str, Any]:
    """
    处理单个文件的线程函数
    
    Args:
        file_info: 文件信息字典，包含文件路径和名称
        headers_level: 标题层级
        max_chunk_len: 最大块长度
        output_folder: 输出文件夹
        file_index: 文件索引
        total_files: 总文件数
        
    Returns:
        处理结果字典
    """
    import json
    from datetime import datetime
    
    file_path = file_info["file_path"]
    file_name = file_info["file_name"]
    file = file_info["file"]
    
    thread_id = threading.current_thread().name
    _thread_safe_print(f"\n[{file_index}/{total_files}] [线程:{thread_id}] 📄 正在处理文件: {file}")
    
    try:
        # 初始化splitter - 每个线程使用独立的实例
        global pic_temp_path
        splitter = MarkdownSplitter(pic_temp_path)
        
        # 使用splitter处理文件
        chunks = splitter.split_file(file_path, headers_level, max_chunk_len)
        
        if not chunks:
            _thread_safe_print(f"  ⚠️ [{thread_id}] 警告: 文件 {file} 没有生成任何chunk")
            return {
                "file_path": file_path,
                "output_file": None,
                "chunks_count": 0,
                "status": "warning",
                "message": "没有生成任何chunk",
                "thread_id": thread_id
            }
        
        # 准备JSON数据
        json_data = {
            "source_file": file,
            "source_path": file_path,
            "processing_time": datetime.now().isoformat(),
            "processing_method": "local",
            "api_response": None,  # 本地版本没有API响应
            "settings": {
                "headers_level": headers_level,
                "chunk_max_len": max_chunk_len,  # 统一字段名
                "add_filename": True  # 本地版本默认添加文件名
            },
            "total_chunks": len(chunks),
            "chunks": []
        }
        
        # 处理每个chunk
        for i, chunk in enumerate(chunks):
            chunk_content = chunk['content']
            chunk_data = {
                "chunk_id": i + 1,
                "content": chunk_content,
                "metadata": chunk.get('metadata', {}),
                "title": "",  # 本地版本暂不提供title
                "chunk_size": len(chunk_content),  # 计算chunk大小
                "supplement": chunk.get('supplement', ''),
                "images": chunk.get('pict', [])  # 图片信息
            }
            json_data["chunks"].append(chunk_data)
        
        # 保存为JSON文件
        json_filename = f"{file_name}_chunks.json"
        json_file_path = os.path.join(output_folder, json_filename)
        
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        result = {
            "file_path": file_path,
            "output_file": json_filename,
            "chunks_count": len(chunks),
            "status": "success",
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ✅ [{thread_id}] 完成处理: {file} -> {json_filename} (包含 {len(chunks)} 个切片)")
        
        return result
        
    except Exception as e:
        error_result = {
            "file_path": file_path,
            "output_file": None,
            "chunks_count": 0,
            "status": "error",
            "error": str(e),
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ❌ [{thread_id}] 处理失败: {file} - {e}")
        return error_result


def custom_download_picture(image_data: bytes, filename: str) -> str:
    """
    自定义图片下载函数（兼容旧代码）
    
    Args:
        image_data: 图片数据
        filename: 文件名
        
    Returns:
        保存后的文件路径
    """
    global pic_temp_path
    processor = ImageProcessor(pic_temp_path)
    return processor.save_image_to_local(image_data, filename)


def file_split(file_path: str, headers_level: int = 6, max_chunk_len: int = 800) -> List[Dict[str, Any]]:
    """
    文件切分主函数（兼容旧代码）
    
    Args:
        file_path: 文件路径
        headers_level: 标题层级
        max_chunk_len: 最大块长度
        
    Returns:
        切分后的块列表
    """
    global pic_temp_path
    splitter = MarkdownSplitter(pic_temp_path)
    return splitter.split_file(file_path, headers_level, max_chunk_len)


def main(input_folder: str, output_folder: str = None, headers_level: int = 6, 
         max_chunk_len: int = 800, supported_extensions: List[str] = None) -> str:
    """
    批量处理文件夹中的文件，将每个文件切分为多个chunk文件
    
    Args:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径（如果为None，则在输入文件夹旁创建）
        headers_level: 标题层级
        max_chunk_len: 最大块长度
        supported_extensions: 支持的文件扩展名列表
        
    Returns:
        输出文件夹路径
        
    Raises:
        FileNotFoundError: 输入文件夹不存在
        RuntimeError: 处理过程中出错
    """
    import json
    from datetime import datetime
    
    # 默认支持的文件扩展名
    if supported_extensions is None:
        supported_extensions = ['.md', '.txt', '.markdown']
    
    # 验证输入文件夹
    if not os.path.exists(input_folder):
        raise FileNotFoundError(f"输入文件夹不存在：{input_folder}")
    
    if not os.path.isdir(input_folder):
        raise ValueError(f"输入路径不是文件夹：{input_folder}")
    
    # 确定输出文件夹
    if output_folder is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_folder = f"{input_folder}_chunks_{timestamp}"
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 初始化splitter
    global pic_temp_path
    splitter = MarkdownSplitter(pic_temp_path)
    
    # 统计信息
    processed_files = 0
    total_chunks = 0
    processing_log = []
    
    try:
        # 遍历输入文件夹中的所有文件
        for root, dirs, files in os.walk(input_folder):
            for file in files:
                file_path = os.path.join(root, file)
                file_name, file_ext = os.path.splitext(file)
                
                # 检查文件扩展名
                if file_ext.lower() not in supported_extensions:
                    continue
                
                try:
                    print(f"正在处理文件: {file_path}")
                    
                    # 使用现有的file_split函数处理文件
                    chunks = splitter.split_file(file_path, headers_level, max_chunk_len)
                    
                    if not chunks:
                        print(f"警告: 文件 {file_path} 没有生成任何chunk")
                        continue
                    
                    # 创建相对路径结构
                    rel_path = os.path.relpath(root, input_folder)
                    if rel_path == '.':
                        output_dir = output_folder
                    else:
                        output_dir = os.path.join(output_folder, rel_path)
                    os.makedirs(output_dir, exist_ok=True)
                    
                    # 保存每个chunk为单独的txt文件
                    file_chunks = 0
                    for i, chunk in enumerate(chunks):
                        chunk_filename = f"chunk_{file_name}_{i+1:03d}.txt"
                        chunk_file_path = os.path.join(output_dir, chunk_filename)
                        
                        # 写入chunk内容
                        with open(chunk_file_path, 'w', encoding='utf-8') as f:
                            f.write(chunk['content'])
                        
                        file_chunks += 1
                        total_chunks += 1
                    
                    processed_files += 1
                    processing_log.append({
                        'file_path': file_path,
                        'chunks_count': file_chunks,
                        'status': 'success'
                    })
                    
                    print(f"完成处理: {file_path} -> 生成 {file_chunks} 个chunk")
                    
                except Exception as e:
                    error_msg = f"处理文件 {file_path} 时出错: {str(e)}"
                    print(f"错误: {error_msg}")
                    processing_log.append({
                        'file_path': file_path,
                        'error': error_msg,
                        'status': 'error'
                    })
                    continue
        
        # 生成处理报告
        report = {
            'input_folder': input_folder,
            'output_folder': output_folder,
            'timestamp': datetime.now().isoformat(),
            'settings': {
                'headers_level': headers_level,
                'max_chunk_len': max_chunk_len,
                'supported_extensions': supported_extensions
            },
            'statistics': {
                'processed_files': processed_files,
                'total_chunks': total_chunks
            },
            'processing_log': processing_log
        }
        
        # 保存处理报告
        report_file = os.path.join(output_folder, 'processing_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 生成README文件
        readme_content = f"""# 文本切分处理结果

## 处理信息
- 输入文件夹: {input_folder}
- 输出文件夹: {output_folder}
- 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 统计信息
- 处理文件数: {processed_files}
- 生成chunk数: {total_chunks}
- 标题层级: {headers_level}
- 最大块长度: {max_chunk_len}

## 支持的文件格式
{', '.join(supported_extensions)}

## 文件命名规则
chunk文件命名格式: `chunk_{{原文件名}}_{{序号}}.txt`

## 详细日志
请查看 `processing_report.json` 文件获取详细的处理日志。
"""
        
        readme_file = os.path.join(output_folder, 'README.md')
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"\n处理完成!")
        print(f"输入文件夹: {input_folder}")
        print(f"输出文件夹: {output_folder}")
        print(f"处理文件数: {processed_files}")
        print(f"生成chunk数: {total_chunks}")
        
        return output_folder
        
    except Exception as e:
        raise RuntimeError(f"批量处理文件时出错: {str(e)}")


def main_json_chunks(input_folder: str, output_folder: str = None, headers_level: int = 6, 
                     max_chunk_len: int = 800, supported_extensions: List[str] = None, max_workers: int = 10) -> str:
    """
    并行批量处理文件夹中的文件，将每个文件切分的chunks保存为一个JSON数组
    
    Args:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径（如果为None，则在输入文件夹旁创建）
        headers_level: 标题层级
        max_chunk_len: 最大块长度
        supported_extensions: 支持的文件扩展名列表
        max_workers: 最大线程数（默认10个）
        
    Returns:
        输出文件夹路径
    """
    import json
    from datetime import datetime
    
    # 默认支持的文件扩展名 - 支持txt格式（解析后的文档）
    if supported_extensions is None:
        supported_extensions = ['.md', '.txt', '.markdown']
    
    # 验证输入文件夹
    if not os.path.exists(input_folder):
        raise FileNotFoundError(f"输入文件夹不存在：{input_folder}")
    
    if not os.path.isdir(input_folder):
        raise ValueError(f"输入路径不是文件夹：{input_folder}")
    
    # 确定输出文件夹
    if output_folder is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_folder = f"{input_folder}_json_chunks_{timestamp}"
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"=== 开始并行批量切分文档为JSON格式 ===")
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件夹: {output_folder}")
    print(f"最大块长度: {max_chunk_len}")
    print(f"标题层级: {headers_level}")
    print(f"使用 {max_workers} 个线程并行处理")
    
    # 收集所有需要处理的文件
    files_to_process = []
    for root, dirs, files in os.walk(input_folder):
        for file in files:
            file_path = os.path.join(root, file)
            file_name, file_ext = os.path.splitext(file)
            
            # 检查文件扩展名
            if file_ext.lower() in supported_extensions:
                files_to_process.append({
                    "file_path": file_path,
                    "file_name": file_name,
                    "file": file
                })
    
    if not files_to_process:
        print(f"在文件夹 {input_folder} 中未找到支持的文件")
        print(f"支持的文件格式: {', '.join(supported_extensions)}")
        return output_folder
    
    print(f"找到 {len(files_to_process)} 个支持的文件")
    
    # 统计信息
    results = []
    completed_count = 0
    
    try:
        # 使用线程池并行处理文件
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="TextSplitter") as executor:
            # 提交所有任务
            future_to_file = {}
            for i, file_info in enumerate(files_to_process, 1):
                future = executor.submit(
                    _process_single_file, 
                    file_info, 
                    headers_level, 
                    max_chunk_len, 
                    output_folder, 
                    i, 
                    len(files_to_process)
                )
                future_to_file[future] = file_info
            
            print(f"\n所有任务已提交，开始并行处理...")
            
            # 收集结果
            for future in as_completed(future_to_file):
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # 显示整体进度
                    progress = completed_count / len(files_to_process) * 100
                    _thread_safe_print(f"\n>>> 整体进度: {completed_count}/{len(files_to_process)} ({progress:.1f}%) 已完成 <<<")
                    
                except Exception as e:
                    file_info = future_to_file[future]
                    error_result = {
                        "file_path": file_info["file_path"],
                        "output_file": None,
                        "chunks_count": 0,
                        "status": "error",
                        "error": f"线程异常: {str(e)}",
                        "thread_id": "unknown"
                    }
                    results.append(error_result)
                    completed_count += 1
                    _thread_safe_print(f"❌ 处理文件时发生异常: {file_info['file']} - {e}")
        
        # 按原始文件顺序排序结果
        file_path_order = {file_info["file_path"]: i for i, file_info in enumerate(files_to_process)}
        results.sort(key=lambda x: file_path_order.get(x["file_path"], float('inf')))
        
        # 统计结果
        success_count = len([r for r in results if r["status"] == "success"])
        error_count = len([r for r in results if r["status"] == "error"])
        warning_count = len([r for r in results if r["status"] == "warning"])
        total_chunks = sum(r.get("chunks_count", 0) for r in results)
        
        # 生成处理报告
        processing_log = []
        for result in results:
            log_entry = {
                'file_path': result["file_path"],
                'output_file': result.get("output_file"),
                'chunks_count': result.get("chunks_count", 0),
                'status': result["status"],
                'thread_id': result.get("thread_id", "unknown")
            }
            if result["status"] == "error":
                log_entry['error'] = result.get("error", "未知错误")
            elif result["status"] == "warning":
                log_entry['message'] = result.get("message", "警告")
            processing_log.append(log_entry)
        
        report = {
            'input_folder': input_folder,
            'output_folder': output_folder,
            'timestamp': datetime.now().isoformat(),
            'settings': {
                'headers_level': headers_level,
                'max_chunk_len': max_chunk_len,
                'supported_extensions': supported_extensions,
                'max_workers': max_workers
            },
            'statistics': {
                'total_files': len(files_to_process),
                'success_files': success_count,
                'error_files': error_count,
                'warning_files': warning_count,
                'total_chunks': total_chunks
            },
            'processing_log': processing_log
        }
        
        # 保存处理报告
        report_file = os.path.join(output_folder, 'processing_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 并行处理完成 ===")
        print(f"总计: {len(files_to_process)} 个文件")
        print(f"✅ 成功: {success_count} 个")
        print(f"❌ 失败: {error_count} 个")
        print(f"⚠️ 警告: {warning_count} 个")
        print(f"📊 生成chunk数: {total_chunks}")
        print(f"🧵 使用线程数: {max_workers}")
        print(f"📁 输出目录: {output_folder}")
        
        if success_count > 0:
            print(f"\n✅ 成功处理的文件:")
            for result in results:
                if result["status"] == "success":
                    thread_info = f"[{result.get('thread_id', 'unknown')}]"
                    print(f"  • {os.path.basename(result['file_path'])} → {result['output_file']} ({result['chunks_count']} 切片) {thread_info}")
        
        if error_count > 0:
            print(f"\n❌ 处理失败的文件:")
            for result in results:
                if result["status"] == "error":
                    thread_info = f"[{result.get('thread_id', 'unknown')}]"
                    print(f"  • {os.path.basename(result['file_path'])} - {result.get('error', '未知错误')} {thread_info}")
        
        print(f"\n💡 说明:")
        print(f"📋 每个文件生成一个JSON文件，包含该文件的所有切片")
        print(f"📊 详细处理日志保存在: processing_report.json")
        
        return output_folder
        
    except Exception as e:
        raise RuntimeError(f"批量处理文件时出错: {str(e)}")


if __name__ == "__main__":
    import sys
    
    # ==================== 配置参数 ====================
    # 在这里直接定义输入文件夹路径
    输入文件夹路径 = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output"  # 修改此路径为你的目标文件夹
    
    # 其他可选参数
    输出文件夹路径 = None     # None表示自动生成，也可以指定具体路径
    标题层级 = 6            # Markdown标题层级
    最大块长度 = 2048       # 每个切片的最大长度
    支持的文件格式 = ['.txt', '.md', '.markdown']  # 支持的文件扩展名
    并行线程数 = 10          # 并行处理的线程数量
    
    # ==================================================
    
    print("=== Markdown文本切分工具（JSON格式输出）===")
    print(f"输入文件夹: {输入文件夹路径}")
    print(f"输出文件夹: {'自动生成' if 输出文件夹路径 is None else 输出文件夹路径}")
    print(f"最大块长度: {最大块长度} 字符")
    print(f"标题层级: {标题层级}")
    print(f"支持格式: {', '.join(支持的文件格式)}")
    print(f"并行线程数: {并行线程数} 个")
    
    try:
        result_folder = main_json_chunks(
            input_folder=输入文件夹路径,
            output_folder=输出文件夹路径, 
            headers_level=标题层级,
            max_chunk_len=最大块长度,
            supported_extensions=支持的文件格式,
            max_workers=并行线程数
        )
        print(f"\n✅ 处理成功完成，结果保存在: {result_folder}")
        print(f"\n💡 使用说明:")
        print(f"1. 每个源文件生成一个对应的JSON文件")
        print(f"2. JSON文件包含该文件的所有切片数组")
        print(f"3. 每个切片包含内容、元数据、图片信息等")
        print(f"4. 🚀 使用{并行线程数}个线程并行处理，显著提升处理速度")
        print(f"5. 🔧 可调整 '并行线程数' 参数来优化性能")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")
        sys.exit(1)


