#!/usr/bin/env python3
"""
检查文件解析前后的数量差异
"""

import os
from pathlib import Path
from collections import defaultdict

def analyze_directory_diff():
    """分析源目录和输出目录的文件差异"""
    
    # 目录路径
    source_dir = Path(r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document")
    output_dir = Path(r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output")
    
    print("=== 文件数量差异分析 ===")
    print(f"源目录: {source_dir}")
    print(f"输出目录: {output_dir}")
    
    # 检查目录是否存在
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return
    
    if not output_dir.exists():
        print(f"❌ 输出目录不存在: {output_dir}")
        return
    
    # 支持的文件扩展名
    supported_extensions = {'.pdf', '.docx', '.doc'}
    
    # 统计源目录中的支持文件
    source_files = []
    all_source_files = []
    
    for file_path in source_dir.iterdir():
        if file_path.is_file():
            all_source_files.append(file_path)
            if file_path.suffix.lower() in supported_extensions:
                source_files.append(file_path)
    
    # 统计输出目录中的txt文件
    output_files = []
    for file_path in output_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() == '.txt':
            output_files.append(file_path)
    
    print(f"\n📊 统计结果:")
    print(f"源目录总文件数: {len(all_source_files)}")
    print(f"源目录支持文件数: {len(source_files)}")
    print(f"输出目录txt文件数: {len(output_files)}")
    print(f"差异: {len(source_files) - len(output_files)} 个文件")
    
    # 按文件类型分组
    source_by_type = defaultdict(list)
    for file_path in source_files:
        source_by_type[file_path.suffix.lower()].append(file_path)
    
    print(f"\n📋 源文件类型分布:")
    for ext, files in source_by_type.items():
        print(f"  {ext}: {len(files)} 个")
    
    # 检查哪些文件没有对应的输出
    source_stems = {f.stem for f in source_files}
    output_stems = {f.stem for f in output_files}
    
    missing_files = source_stems - output_stems
    extra_files = output_stems - source_stems
    
    if missing_files:
        print(f"\n❌ 缺失的输出文件 ({len(missing_files)} 个):")
        for stem in sorted(missing_files):
            # 找到对应的源文件
            for source_file in source_files:
                if source_file.stem == stem:
                    print(f"  • {source_file.name} ({source_file.suffix})")
                    break
    
    if extra_files:
        print(f"\n⚠️ 多余的输出文件 ({len(extra_files)} 个):")
        for stem in sorted(extra_files):
            print(f"  • {stem}.txt")
    
    # 检查文件名长度和特殊字符
    print(f"\n🔍 文件名分析:")
    problematic_files = []
    
    for source_file in source_files:
        # 检查文件名长度
        if len(source_file.name) > 200:
            problematic_files.append((source_file, "文件名过长"))
        
        # 检查特殊字符
        problematic_chars = ['<', '>', ':', '"', '|', '?', '*']
        if any(char in source_file.name for char in problematic_chars):
            problematic_files.append((source_file, "包含特殊字符"))
        
        # 检查是否以点开头
        if source_file.name.startswith('.'):
            problematic_files.append((source_file, "隐藏文件"))
    
    if problematic_files:
        print(f"  发现 {len(problematic_files)} 个可能有问题的文件:")
        for file_path, reason in problematic_files:
            print(f"    • {file_path.name} - {reason}")
    else:
        print("  所有文件名格式正常")
    
    # 检查文件大小
    print(f"\n📏 文件大小分析:")
    empty_files = []
    large_files = []
    
    for source_file in source_files:
        try:
            size = source_file.stat().st_size
            if size == 0:
                empty_files.append(source_file)
            elif size > 50 * 1024 * 1024:  # 50MB
                large_files.append((source_file, size))
        except Exception as e:
            print(f"    无法读取文件大小: {source_file.name} - {e}")
    
    if empty_files:
        print(f"  空文件 ({len(empty_files)} 个):")
        for file_path in empty_files:
            print(f"    • {file_path.name}")
    
    if large_files:
        print(f"  大文件 ({len(large_files)} 个):")
        for file_path, size in large_files:
            size_mb = size / (1024 * 1024)
            print(f"    • {file_path.name} ({size_mb:.1f} MB)")
    
    # 检查重复文件名（不同扩展名）
    print(f"\n🔄 重复文件名检查:")
    stem_count = defaultdict(list)
    for source_file in source_files:
        stem_count[source_file.stem].append(source_file)
    
    duplicates = {stem: files for stem, files in stem_count.items() if len(files) > 1}
    if duplicates:
        print(f"  发现 {len(duplicates)} 组重复文件名:")
        for stem, files in duplicates.items():
            print(f"    • {stem}:")
            for file_path in files:
                print(f"      - {file_path.name}")
        print("  注意: 重复文件名可能导致输出文件覆盖!")
    else:
        print("  没有重复的文件名")

if __name__ == "__main__":
    analyze_directory_diff()
