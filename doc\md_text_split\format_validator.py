#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出格式验证工具

用于验证两个脚本的输出格式是否一致
"""

import json
import sys
from typing import Dict, List, Any, Set

class FormatValidator:
    """格式验证器"""
    
    def __init__(self):
        self.required_top_fields = {
            "source_file", "source_path", "processing_time", 
            "processing_method", "api_response", "settings", 
            "total_chunks", "chunks"
        }
        
        self.required_chunk_fields = {
            "chunk_id", "content", "metadata", "title", 
            "chunk_size", "supplement", "images"
        }
        
        self.required_settings_fields = {
            "headers_level", "chunk_max_len", "add_filename"
        }
    
    def validate_json_file(self, file_path: str) -> Dict[str, Any]:
        """
        验证JSON文件格式
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            验证结果字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            return {
                "valid": False,
                "error": f"文件读取失败: {e}",
                "details": {}
            }
        
        return self.validate_data(data)
    
    def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证数据格式
        
        Args:
            data: 要验证的数据
            
        Returns:
            验证结果字典
        """
        result = {
            "valid": True,
            "error": None,
            "details": {
                "missing_top_fields": [],
                "missing_chunk_fields": [],
                "missing_settings_fields": [],
                "processing_method": None,
                "total_chunks": 0,
                "chunk_count": 0,
                "api_response_present": False,
                "has_images": False,
                "has_supplement": False
            }
        }
        
        # 检查顶级字段
        missing_top = self.required_top_fields - set(data.keys())
        if missing_top:
            result["valid"] = False
            result["details"]["missing_top_fields"] = list(missing_top)
        
        # 检查processing_method
        if "processing_method" in data:
            method = data["processing_method"]
            result["details"]["processing_method"] = method
            if method not in ["api", "local"]:
                result["valid"] = False
                result["error"] = f"无效的processing_method: {method}"
        
        # 检查settings字段
        if "settings" in data:
            settings = data["settings"]
            missing_settings = self.required_settings_fields - set(settings.keys())
            if missing_settings:
                result["valid"] = False
                result["details"]["missing_settings_fields"] = list(missing_settings)
        
        # 检查api_response
        if "api_response" in data:
            api_response = data["api_response"]
            result["details"]["api_response_present"] = api_response is not None
            
            # 如果是API方式，api_response不应该为None
            if data.get("processing_method") == "api" and api_response is None:
                result["valid"] = False
                result["error"] = "API方式处理但api_response为None"
            
            # 如果是本地方式，api_response应该为None
            if data.get("processing_method") == "local" and api_response is not None:
                result["valid"] = False
                result["error"] = "本地方式处理但api_response不为None"
        
        # 检查chunks
        if "chunks" in data:
            chunks = data["chunks"]
            result["details"]["chunk_count"] = len(chunks)
            
            if "total_chunks" in data:
                total_chunks = data["total_chunks"]
                result["details"]["total_chunks"] = total_chunks
                
                if len(chunks) != total_chunks:
                    result["valid"] = False
                    result["error"] = f"chunks数量({len(chunks)})与total_chunks({total_chunks})不匹配"
            
            # 检查每个chunk的字段
            for i, chunk in enumerate(chunks):
                missing_chunk = self.required_chunk_fields - set(chunk.keys())
                if missing_chunk:
                    result["valid"] = False
                    result["details"]["missing_chunk_fields"].extend([
                        f"chunk[{i}].{field}" for field in missing_chunk
                    ])
                
                # 检查是否有图片和补充信息
                if chunk.get("images") and len(chunk["images"]) > 0:
                    result["details"]["has_images"] = True
                
                if chunk.get("supplement") and chunk["supplement"].strip():
                    result["details"]["has_supplement"] = True
                
                # 验证chunk_size
                if "content" in chunk and "chunk_size" in chunk:
                    actual_size = len(chunk["content"])
                    declared_size = chunk["chunk_size"]
                    if actual_size != declared_size:
                        result["valid"] = False
                        result["error"] = f"chunk[{i}] chunk_size({declared_size})与实际内容长度({actual_size})不匹配"
        
        return result
    
    def compare_formats(self, file1: str, file2: str) -> Dict[str, Any]:
        """
        比较两个文件的格式
        
        Args:
            file1: 第一个文件路径
            file2: 第二个文件路径
            
        Returns:
            比较结果
        """
        result1 = self.validate_json_file(file1)
        result2 = self.validate_json_file(file2)
        
        return {
            "file1": {
                "path": file1,
                "validation": result1
            },
            "file2": {
                "path": file2,
                "validation": result2
            },
            "format_compatible": result1["valid"] and result2["valid"],
            "differences": self._find_differences(result1, result2)
        }
    
    def _find_differences(self, result1: Dict[str, Any], result2: Dict[str, Any]) -> List[str]:
        """找出两个验证结果的差异"""
        differences = []
        
        # 比较处理方式
        method1 = result1["details"].get("processing_method")
        method2 = result2["details"].get("processing_method")
        if method1 != method2:
            differences.append(f"处理方式不同: {method1} vs {method2}")
        
        # 比较字段完整性
        missing1 = result1["details"].get("missing_top_fields", [])
        missing2 = result2["details"].get("missing_top_fields", [])
        if set(missing1) != set(missing2):
            differences.append(f"缺失的顶级字段不同: {missing1} vs {missing2}")
        
        return differences
    
    def generate_report(self, validation_result: Dict[str, Any]) -> str:
        """生成验证报告"""
        details = validation_result["details"]
        
        report = "📋 格式验证报告\n"
        report += "=" * 50 + "\n"
        
        if validation_result["valid"]:
            report += "✅ 格式验证通过\n\n"
        else:
            report += "❌ 格式验证失败\n"
            if validation_result["error"]:
                report += f"错误: {validation_result['error']}\n\n"
        
        report += f"📊 基本信息:\n"
        report += f"- 处理方式: {details['processing_method']}\n"
        report += f"- 总chunks数: {details['total_chunks']}\n"
        report += f"- 实际chunks数: {details['chunk_count']}\n"
        report += f"- API响应存在: {details['api_response_present']}\n"
        report += f"- 包含图片: {details['has_images']}\n"
        report += f"- 包含补充信息: {details['has_supplement']}\n\n"
        
        if details["missing_top_fields"]:
            report += f"❌ 缺失的顶级字段: {', '.join(details['missing_top_fields'])}\n"
        
        if details["missing_chunk_fields"]:
            report += f"❌ 缺失的chunk字段: {', '.join(details['missing_chunk_fields'])}\n"
        
        if details["missing_settings_fields"]:
            report += f"❌ 缺失的settings字段: {', '.join(details['missing_settings_fields'])}\n"
        
        return report


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  验证单个文件: python format_validator.py <json_file>")
        print("  比较两个文件: python format_validator.py <file1> <file2>")
        print()
        print("示例:")
        print("  python format_validator.py output.json")
        print("  python format_validator.py api_output.json local_output.json")
        sys.exit(1)
    
    validator = FormatValidator()
    
    if len(sys.argv) == 2:
        # 验证单个文件
        file_path = sys.argv[1]
        print(f"🔍 验证文件: {file_path}")
        result = validator.validate_json_file(file_path)
        report = validator.generate_report(result)
        print(report)
        
    elif len(sys.argv) == 3:
        # 比较两个文件
        file1, file2 = sys.argv[1], sys.argv[2]
        print(f"🔍 比较文件格式:")
        print(f"  文件1: {file1}")
        print(f"  文件2: {file2}")
        print()
        
        comparison = validator.compare_formats(file1, file2)
        
        print("📋 文件1验证结果:")
        print(validator.generate_report(comparison["file1"]["validation"]))
        
        print("\n📋 文件2验证结果:")
        print(validator.generate_report(comparison["file2"]["validation"]))
        
        print(f"\n🔄 格式兼容性: {'✅ 兼容' if comparison['format_compatible'] else '❌ 不兼容'}")
        
        if comparison["differences"]:
            print(f"\n⚠️ 发现差异:")
            for diff in comparison["differences"]:
                print(f"  • {diff}")
        else:
            print(f"\n✅ 两个文件格式完全一致")


if __name__ == "__main__":
    main()
