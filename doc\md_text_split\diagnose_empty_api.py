#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API返回空数据诊断工具

用于分析和诊断文档切分API返回空数据的原因
"""

import os
import requests
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import chardet

# API配置
API_URL = "https://llm.dt.zte.com.cn/zte-ibo-acm-tools/cut"
API_HEADERS = {'Content-Type': 'application/json'}

class APIDiagnostic:
    """API诊断类"""
    
    def __init__(self):
        self.issues = []
        self.recommendations = []
        
    def add_issue(self, issue: str, level: str = "warning"):
        """添加问题"""
        self.issues.append({"issue": issue, "level": level})
        
    def add_recommendation(self, recommendation: str):
        """添加建议"""
        self.recommendations.append(recommendation)
        
    def diagnose_file(self, file_path: str) -> Dict[str, Any]:
        """诊断单个文件"""
        print(f"\n🔍 诊断文件: {file_path}")
        
        if not os.path.exists(file_path):
            self.add_issue("文件不存在", "error")
            return {"status": "error", "message": "文件不存在"}
            
        # 1. 检查文件大小
        file_size = os.path.getsize(file_path)
        print(f"📏 文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
        
        if file_size == 0:
            self.add_issue("文件为空", "error")
            return {"status": "error", "message": "文件为空"}
            
        if file_size > 1024 * 1024:  # 1MB
            self.add_issue(f"文件过大 ({file_size/1024/1024:.2f} MB)，可能影响API处理", "warning")
            self.add_recommendation("考虑先手动分割大文件")
            
        # 2. 检查文件编码
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB检测编码
                
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result['encoding']
            confidence = encoding_result['confidence']
            
            print(f"📝 文件编码: {encoding} (置信度: {confidence:.2f})")
            
            if encoding.lower() not in ['utf-8', 'utf-8-sig']:
                self.add_issue(f"文件编码为 {encoding}，建议使用UTF-8", "warning")
                self.add_recommendation("将文件转换为UTF-8编码")
                
        except Exception as e:
            self.add_issue(f"检测文件编码失败: {e}", "warning")
            
        # 3. 检查文件内容
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            content_length = len(content)
            print(f"📄 文件内容长度: {content_length} 字符")
            
            if content_length < 10:
                self.add_issue("文件内容过短，可能无法有效切分", "warning")
                
            # 检查内容类型
            lines = content.split('\n')
            non_empty_lines = [line.strip() for line in lines if line.strip()]
            
            print(f"📋 有效行数: {len(non_empty_lines)}")
            
            # 检查是否包含标题标记
            has_markdown_headers = any(line.strip().startswith('#') for line in lines)
            print(f"🏷️ 包含Markdown标题: {'是' if has_markdown_headers else '否'}")
            
            if not has_markdown_headers:
                self.add_issue("文档缺少Markdown格式的标题", "info")
                self.add_recommendation("添加Markdown格式标题 (# ## ###) 可能提高切分效果")
                
            # 检查是否主要是特殊字符
            text_chars = sum(1 for c in content if c.isalnum() or c.isspace())
            special_chars = len(content) - text_chars
            special_ratio = special_chars / len(content) if len(content) > 0 else 0
            
            print(f"🔤 特殊字符比例: {special_ratio:.2%}")
            
            if special_ratio > 0.5:
                self.add_issue("文档包含大量特殊字符，可能影响API处理", "warning")
                self.add_recommendation("检查文档是否为纯文本格式")
                
            return {
                "status": "success",
                "content": content,
                "stats": {
                    "file_size": file_size,
                    "content_length": content_length,
                    "lines": len(lines),
                    "non_empty_lines": len(non_empty_lines),
                    "has_markdown_headers": has_markdown_headers,
                    "special_char_ratio": special_ratio,
                    "encoding": encoding
                }
            }
            
        except Exception as e:
            self.add_issue(f"读取文件内容失败: {e}", "error")
            return {"status": "error", "message": f"读取文件失败: {e}"}
            
    def test_api_connectivity(self) -> bool:
        """测试API连接性"""
        print(f"\n🌐 测试API连接: {API_URL}")
        
        test_data = {
            "file_texts": [{"title": "测试", "content": "# 测试标题\n这是一个测试文档。"}],
            "headers_level": 6,
            "chunk_max_len": 1024,
            "add_filename": True
        }
        
        try:
            response = requests.post(API_URL, headers=API_HEADERS, json=test_data, timeout=30)
            print(f"📊 HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查code字段
                if "code" in result:
                    code_info = result["code"]
                    if code_info.get("code") != "0000":
                        print(f"⚠️ API返回错误: {code_info.get('msg', '未知错误')}")
                        self.add_issue(f"API返回错误: {code_info.get('msg')}", "warning")
                        return False
                
                # 检查bo字段
                if "bo" in result and result["bo"]:
                    print("✅ API连接正常且响应有效")
                    return True
                else:
                    print("⚠️ API连接正常但返回空数据")
                    self.add_issue("API测试返回空数据", "warning")
                    return False
            else:
                print(f"❌ API返回错误状态码: {response.status_code}")
                self.add_issue(f"API状态码错误: {response.status_code}", "error")
                return False
                
        except requests.exceptions.Timeout:
            print("⏱️ API请求超时")
            self.add_issue("API请求超时", "error")
            self.add_recommendation("检查网络连接或尝试增加超时时间")
            return False
            
        except requests.exceptions.ConnectionError:
            print("🔌 API连接失败")
            self.add_issue("无法连接到API服务器", "error")
            self.add_recommendation("检查网络连接和API服务器状态")
            return False
            
        except Exception as e:
            print(f"❌ API测试失败: {e}")
            self.add_issue(f"API测试异常: {e}", "error")
            return False
            
    def test_file_with_api(self, file_path: str, content: str) -> Dict[str, Any]:
        """使用API测试文件"""
        print(f"\n🧪 API测试文件: {os.path.basename(file_path)}")
        
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # 测试不同参数组合
        test_configs = [
            {"chunk_max_len": 2048, "headers_level": 6},
            {"chunk_max_len": 1024, "headers_level": 6},
            {"chunk_max_len": 512, "headers_level": 6},
            {"chunk_max_len": 2048, "headers_level": 3},
        ]
        
        for i, config in enumerate(test_configs, 1):
            print(f"  📋 测试配置 {i}: chunk_max_len={config['chunk_max_len']}, headers_level={config['headers_level']}")
            
            test_data = {
                "file_texts": [{"title": file_name, "content": content}],
                "headers_level": config["headers_level"],
                "chunk_max_len": config["chunk_max_len"],
                "add_filename": True
            }
            
            try:
                response = requests.post(API_URL, headers=API_HEADERS, json=test_data, timeout=60)
                response.raise_for_status()
                result = response.json()
                
                # 检查code字段
                if "code" in result:
                    code_info = result["code"]
                    if code_info.get("code") != "0000":
                        print(f"    ❌ API错误: {code_info.get('msg', '未知错误')}")
                        continue
                
                # 检查bo字段
                if "bo" in result and result["bo"]:
                    chunks_count = len(result['bo'])
                    print(f"    ✅ 成功: 生成 {chunks_count} 个切片")
                    return {"status": "success", "config": config, "chunks": chunks_count}
                else:
                    print(f"    ⚠️ 返回空数据")
                    
            except Exception as e:
                print(f"    ❌ 失败: {e}")
                
        self.add_issue("所有配置都返回空数据或失败", "warning")
        self.add_recommendation("文档内容可能不适合当前API的切分算法")
        return {"status": "failed", "message": "所有配置测试失败"}
        
    def generate_report(self) -> str:
        """生成诊断报告"""
        report = "\n" + "="*60 + "\n"
        report += "📋 API诊断报告\n"
        report += "="*60 + "\n"
        
        if self.issues:
            report += "\n🔍 发现的问题:\n"
            for i, issue in enumerate(self.issues, 1):
                level_icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(issue["level"], "•")
                report += f"{i:2d}. {level_icon} {issue['issue']}\n"
                
        if self.recommendations:
            report += "\n💡 建议:\n"
            for i, rec in enumerate(self.recommendations, 1):
                report += f"{i:2d}. 🔧 {rec}\n"
                
        if not self.issues:
            report += "\n✅ 未发现明显问题\n"
            
        return report


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python diagnose_empty_api.py <文件路径>")
        print("示例: python diagnose_empty_api.py document.txt")
        sys.exit(1)
        
    file_path = sys.argv[1]
    
    diagnostic = APIDiagnostic()
    
    print("🔍 开始API诊断...")
    
    # 1. 测试API连接
    api_ok = diagnostic.test_api_connectivity()
    
    # 2. 诊断文件
    file_result = diagnostic.diagnose_file(file_path)
    
    # 3. 如果API和文件都正常，进行API测试
    if api_ok and file_result["status"] == "success":
        diagnostic.test_file_with_api(file_path, file_result["content"])
        
    # 4. 生成报告
    report = diagnostic.generate_report()
    print(report)
    
    # 5. 保存报告
    report_file = f"diagnostic_report_{os.path.splitext(os.path.basename(file_path))[0]}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n📄 诊断报告已保存: {report_file}")


if __name__ == "__main__":
    main()
