#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档切分服务调用示例
演示如何使用 document_splitter.py 脚本
"""

import os
from document_splitter import DocumentSplitter


def example_usage():
    """使用示例"""
    
    print("文档切分服务调用示例")
    print("=" * 50)
    
    # 1. 基本用法示例
    print("\n1. 基本用法示例:")
    print("python document_splitter.py ./doc")
    
    # 2. 指定参数的用法示例  
    print("\n2. 指定参数的用法示例:")
    print("python document_splitter.py ./doc --headers-level 3 --chunk-max-len 1024 --add-filename --api-key YOUR_API_KEY")
    
    # 3. 保存结果到文件
    print("\n3. 保存结果到文件:")
    print("python document_splitter.py ./doc --output result.txt")
    
    # 4. 使用自定义接口地址
    print("\n4. 使用自定义接口地址:")
    print("python document_splitter.py ./doc --api-url https://custom.api.com/cut")
    
    # 5. 编程方式调用示例
    print("\n5. 编程方式调用示例:")
    
    # 创建文档切分器实例
    splitter = DocumentSplitter()
    
    # 假设我们有一些测试文件夹
    test_folder = "./text_chunks/test"
    
    if os.path.exists(test_folder):
        print(f"正在处理文件夹: {test_folder}")
        
        # 扫描文件夹
        file_texts = splitter.scan_folder(test_folder)
        
        if file_texts:
            # 调用接口
            result = splitter.call_split_api(
                file_texts=file_texts,
                headers_level=2,
                chunk_max_len=800,
                add_filename=True
            )
            
            # 输出结果
            splitter.format_output(result)
        else:
            print("未找到可处理的文件")
    else:
        print(f"测试文件夹不存在: {test_folder}")
    
    print("\n参数说明:")
    print("-" * 30)
    print("folder_path      : 要处理的文件夹路径")
    print("--api-url        : 文档切分接口地址")
    print("--headers-level  : 切分标题层级 (1-6, 默认: 2)")
    print("--chunk-max-len  : 最大切片长度 (100-2048, 默认: 2048)")
    print("--add-filename   : 在切片中添加文件名")
    print("--output         : 输出文件路径 (可选)")
    
    print("\n支持的文件格式:")
    print("-" * 30)
    print(".txt, .md, .markdown, .text, .py, .js, .java")
    print(".cpp, .c, .h, .hpp, .css, .html, .xml, .json")
    print(".yaml, .yml, .ini, .cfg, .conf, .log")


if __name__ == "__main__":
    example_usage()
