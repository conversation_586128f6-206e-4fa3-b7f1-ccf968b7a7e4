# Chunk对比快速指南

## 🎯 目标
对比两个文件夹中相同文档的chunk切分结果，评估API版本和本地版本的一致性。

## 📁 您的文件夹
```
API版本输出:   D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_api_chunks_20250907_170250
本地版本输出: D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_json_chunks_20250907_170302
```

## 🚀 三种使用方式

### 方式1: 一键运行（推荐）
```bash
python run_comparison.py
```
- ✅ 最简单，直接运行
- ✅ 已配置好您的文件夹路径
- ✅ 自动生成报告

### 方式2: 灵活对比
```bash
python compare_chunk_folders.py
```
- ✅ 支持默认路径和自定义路径
- ✅ 可以先分析文件夹内容

### 方式3: 通用工具
```bash
python chunk_comparator.py batch <api_folder> <local_folder>
```
- ✅ 完整功能的对比工具
- ✅ 支持任意文件夹对比

## 📊 输出结果

### 报告文件
- `chunk_comparison_report_YYYYMMDD_HHMMSS.txt` - 详细对比报告
- `chunk_comparison_data_YYYYMMDD_HHMMSS.json` - 原始数据

### 关键指标
1. **综合得分**: 0-1之间，越高越好
2. **质量分布**: 优秀/良好/一般/较差的文件数量
3. **平均指标**: 覆盖率、相似度、一致性等

### 评价标准
- 🎉 **优秀 (>0.9)**: 一致性很好，可互相验证
- 👍 **良好 (0.7-0.9)**: 整体效果不错
- ⚠️ **一般 (0.5-0.7)**: 存在一定差异，需要关注
- ❌ **较差 (<0.5)**: 差异较大，需要深入分析

## 💡 使用建议

### 1. 首次运行
```bash
# 直接运行，查看整体情况
python run_comparison.py
```

### 2. 分析结果
查看生成的报告文件，重点关注：
- 综合得分是否满意
- 问题文件列表
- 具体差异原因

### 3. 问题诊断
对于得分较低的文件：
- 检查原始文档是否有特殊格式
- 对比两种方法的处理差异
- 调整切分参数重新处理

### 4. 参数优化
根据对比结果调整：
- chunk_max_len (块大小)
- headers_level (标题层级)
- 其他处理参数

## ⚡ 快速命令

```bash
# 1. 一键对比
python run_comparison.py

# 2. 快速分析文件夹
python compare_chunk_folders.py analyze

# 3. 对比其他文件夹
python chunk_comparator.py batch ./folder1 ./folder2

# 4. 单文件对比
python chunk_comparator.py compare file1.json file2.json
```

## 🔍 常见问题

### Q1: 显示"未找到可对比的文件对"
**原因**: 两个文件夹中没有相同名称的文件
**解决**: 检查文件命名是否一致，确保文件格式为 `*_chunks.json`

### Q2: 得分很低怎么办？
**原因**: 两种方法的切分策略差异较大
**解决**: 
1. 检查切分参数是否一致
2. 分析具体差异原因
3. 考虑调整处理逻辑

### Q3: 某些文件只在一个文件夹中存在
**原因**: 处理过程中部分文件失败或被跳过
**解决**: 检查处理日志，重新处理缺失的文件

## 📋 结果解读

### 优秀结果 (>0.9)
- 说明两种方法高度一致
- 可以互相验证结果
- 推荐使用任一方法

### 良好结果 (0.7-0.9)
- 整体一致性不错
- 可能存在个别差异
- 建议人工检查差异文件

### 一般结果 (0.5-0.7)
- 存在明显差异
- 需要分析差异原因
- 考虑参数调优

### 较差结果 (<0.5)
- 差异很大
- 需要深入分析处理逻辑
- 可能需要重新设计策略

## 🎉 开始使用

1. **确认文件夹路径正确**
2. **运行**: `python run_comparison.py`
3. **查看报告**: 检查生成的txt报告
4. **分析结果**: 关注得分和问题文件
5. **优化改进**: 根据建议调整参数

祝您对比顺利！ 🚀
