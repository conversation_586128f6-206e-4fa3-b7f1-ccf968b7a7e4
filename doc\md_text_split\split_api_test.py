import requests
import json
import os
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Any
from datetime import datetime
from pathlib import Path

# API配置
API_URL = "https://llm.dt.zte.com.cn/zte-ibo-acm-tools/cut"
API_HEADERS = {
    'Content-Type': 'application/json'
}

# 线程安全的打印锁
_print_lock = threading.Lock()

def _thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with _print_lock:
        print(*args, **kwargs)


# 示例内容已移除，减少文件大小




def call_split_api(content: str, title: str, headers_level: int = 6, 
                  chunk_max_len: int = 2048, add_filename: bool = True) -> Dict[str, Any]:
    """
    调用切分API处理单个文档内容
    
    Args:
        content: 文档内容
        title: 文档标题
        headers_level: 标题层级
        chunk_max_len: 最大块长度
        add_filename: 是否添加文件名
        
    Returns:
        API响应结果
        
    Raises:
        Exception: API调用失败时抛出异常
    """
    data = {
        "file_texts": [
            {
                "title": title,
                "content": content
            }
        ],
        "headers_level": headers_level,
        "chunk_max_len": chunk_max_len,
        "add_filename": add_filename
    }
    
    try:
        response = requests.post(API_URL, headers=API_HEADERS, json=data, timeout=120)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        raise Exception(f"API调用失败: {e}")


def _process_single_file_api(file_info: Dict[str, str], headers_level: int, chunk_max_len: int, 
                            add_filename: bool, output_folder: str, file_index: int, total_files: int) -> Dict[str, Any]:
    """
    使用API处理单个文件的线程函数
    
    Args:
        file_info: 文件信息字典
        headers_level: 标题层级
        chunk_max_len: 最大块长度
        add_filename: 是否添加文件名
        output_folder: 输出文件夹
        file_index: 文件索引
        total_files: 总文件数
        
    Returns:
        处理结果字典
    """
    file_path = file_info["file_path"]
    file_name = file_info["file_name"]
    file = file_info["file"]
    
    thread_id = threading.current_thread().name
    _thread_safe_print(f"\n[{file_index}/{total_files}] [线程:{thread_id}] 📄 正在处理文件: {file}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 调用API进行切分
        api_response = call_split_api(content, file_name, headers_level, chunk_max_len, add_filename)
        
        # 检查API响应
        if "code" in api_response:
            code_info = api_response["code"]
            if code_info.get("code") != "0000":
                _thread_safe_print(f"  ⚠️ [{thread_id}] API错误: {file} - {code_info.get('msg', '未知错误')}")
                return {
                    "file_path": file_path,
                    "output_file": None,
                    "chunks_count": 0,
                    "status": "warning",
                    "message": f"API错误: {code_info.get('msg', '未知错误')}",
                    "thread_id": thread_id
                }
        
        # 获取切分数据
        bo_data = api_response.get("bo", [])
        
        # bo数组中通常只有一个元素（对应传入的一个文件），该元素包含多个chunk字符串
        file_chunks = bo_data[0] if len(bo_data) > 0 else []
        
        json_data = {
            "source_file": file,
            "source_path": file_path,
            "processing_time": datetime.now().isoformat(),
            "processing_method": "api",
            "api_response": api_response,
            "settings": {
                "headers_level": headers_level,
                "chunk_max_len": chunk_max_len,
                "add_filename": add_filename
            },
            "total_chunks": len(file_chunks),
            "chunks": []
        }
        
        # 处理每个chunk - file_chunks是包含多个chunk字符串的数组
        for i, chunk_content in enumerate(file_chunks):
            chunk_data = {
                "chunk_id": i + 1,
                "content": chunk_content,
                "metadata": {},  # API响应中没有提供metadata
                "title": "",     # API响应中没有提供title
                "chunk_size": len(chunk_content),
                "supplement": "",  # API版本没有supplement，设为空字符串
                "images": []       # API版本没有图片信息，设为空数组
            }
            json_data["chunks"].append(chunk_data)
        
        # 保存为JSON文件
        json_filename = f"{file_name}_chunks.json"
        json_file_path = os.path.join(output_folder, json_filename)
        
        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        result = {
            "file_path": file_path,
            "output_file": json_filename,
            "chunks_count": len(file_chunks),
            "status": "success",
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ✅ [{thread_id}] 完成处理: {file} -> {json_filename} (包含 {len(file_chunks)} 个切片)")
        
        return result
        
    except Exception as e:
        error_result = {
            "file_path": file_path,
            "output_file": None,
            "chunks_count": 0,
            "status": "error",
            "error": str(e),
            "thread_id": thread_id
        }
        
        _thread_safe_print(f"  ❌ [{thread_id}] 处理失败: {file} - {e}")
        return error_result


# 测试数据（简化版）
data_example = {
    "file_texts": [
        {
            "title": "测试文档",
            "content": "# 测试标题\n\n这是一个测试文档的内容。"
        }
    ],
    "headers_level": 6,
    "chunk_max_len": 2048,
    "add_filename": True
}


def main_api_chunks(input_folder: str, output_folder: str = None, headers_level: int = 6, 
                   chunk_max_len: int = 2048, add_filename: bool = True, 
                   supported_extensions: List[str] = None, max_workers: int = 10) -> str:
    """
    并行批量处理文件夹中的文件，使用API切分并保存为JSON数组
    
    Args:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径（如果为None，则自动生成）
        headers_level: 标题层级
        chunk_max_len: 最大块长度
        add_filename: 是否添加文件名
        supported_extensions: 支持的文件扩展名列表
        max_workers: 最大线程数（默认10个）
        
    Returns:
        输出文件夹路径
    """
    # 默认支持的文件扩展名 - 支持更多文本格式
    if supported_extensions is None:
        supported_extensions = ['.txt', '.md', '.markdown', '.text', '.rtf', '.log', '.csv', '.tsv', '.xml', '.json', '.html', '.htm', '.py', '.js', '.css', '.sql', '.sh', '.bat', '.yaml', '.yml', '.ini', '.cfg', '.conf']
    
    # 验证输入文件夹
    if not os.path.exists(input_folder):
        raise FileNotFoundError(f"输入文件夹不存在：{input_folder}")
    
    if not os.path.isdir(input_folder):
        raise ValueError(f"输入路径不是文件夹：{input_folder}")
    
    # 确定输出文件夹
    if output_folder is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_folder = f"{input_folder}_api_chunks_{timestamp}"
    
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    print(f"=== 开始API并行批量切分文档为JSON格式 ===")
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件夹: {output_folder}")
    print(f"API地址: {API_URL}")
    print(f"最大块长度: {chunk_max_len}")
    print(f"标题层级: {headers_level}")
    print(f"使用 {max_workers} 个线程并行处理")
    
    # 收集所有需要处理的文件
    files_to_process = []
    for root, dirs, files in os.walk(input_folder):
        for file in files:
            file_path = os.path.join(root, file)
            file_name, file_ext = os.path.splitext(file)
            
            # 检查文件扩展名
            if file_ext.lower() in supported_extensions:
                files_to_process.append({
                    "file_path": file_path,
                    "file_name": file_name,
                    "file": file
                })
    
    if not files_to_process:
        print(f"在文件夹 {input_folder} 中未找到支持的文件")
        print(f"支持的文件格式: {', '.join(supported_extensions)}")
        return output_folder
    
    print(f"找到 {len(files_to_process)} 个支持的文件")
    
    # 统计信息
    results = []
    completed_count = 0
    
    try:
        # 使用线程池并行处理文件
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="APISplitter") as executor:
            # 提交所有任务
            future_to_file = {}
            for i, file_info in enumerate(files_to_process, 1):
                future = executor.submit(
                    _process_single_file_api, 
                    file_info, 
                    headers_level, 
                    chunk_max_len, 
                    add_filename,
                    output_folder, 
                    i, 
                    len(files_to_process)
                )
                future_to_file[future] = file_info
            
            print(f"\n所有任务已提交，开始并行调用API处理...")
            
            # 收集结果
            for future in as_completed(future_to_file):
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # 显示整体进度
                    progress = completed_count / len(files_to_process) * 100
                    _thread_safe_print(f"\n>>> 整体进度: {completed_count}/{len(files_to_process)} ({progress:.1f}%) 已完成 <<<")
                    
                except Exception as e:
                    file_info = future_to_file[future]
                    error_result = {
                        "file_path": file_info["file_path"],
                        "output_file": None,
                        "chunks_count": 0,
                        "status": "error",
                        "error": f"线程异常: {str(e)}",
                        "thread_id": "unknown"
                    }
                    results.append(error_result)
                    completed_count += 1
                    _thread_safe_print(f"❌ 处理文件时发生异常: {file_info['file']} - {e}")
        
        # 按原始文件顺序排序结果
        file_path_order = {file_info["file_path"]: i for i, file_info in enumerate(files_to_process)}
        results.sort(key=lambda x: file_path_order.get(x["file_path"], float('inf')))
        
        # 统计结果
        success_count = len([r for r in results if r["status"] == "success"])
        error_count = len([r for r in results if r["status"] == "error"])
        warning_count = len([r for r in results if r["status"] == "warning"])
        total_chunks = sum(r.get("chunks_count", 0) for r in results)
        
        # 生成处理报告
        processing_log = []
        for result in results:
            log_entry = {
                'file_path': result["file_path"],
                'output_file': result.get("output_file"),
                'chunks_count': result.get("chunks_count", 0),
                'status': result["status"],
                'thread_id': result.get("thread_id", "unknown")
            }
            if result["status"] == "error":
                log_entry['error'] = result.get("error", "未知错误")
            elif result["status"] == "warning":
                log_entry['message'] = result.get("message", "警告")
            processing_log.append(log_entry)
        
        report = {
            'input_folder': input_folder,
            'output_folder': output_folder,
            'api_url': API_URL,
            'timestamp': datetime.now().isoformat(),
            'settings': {
                'headers_level': headers_level,
                'chunk_max_len': chunk_max_len,
                'add_filename': add_filename,
                'supported_extensions': supported_extensions,
                'max_workers': max_workers
            },
            'statistics': {
                'total_files': len(files_to_process),
                'success_files': success_count,
                'error_files': error_count,
                'warning_files': warning_count,
                'total_chunks': total_chunks
            },
            'processing_log': processing_log
        }
        
        # 保存处理报告
        report_file = os.path.join(output_folder, 'processing_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== API并行处理完成 ===")
        print(f"总计: {len(files_to_process)} 个文件")
        print(f"✅ 成功: {success_count} 个")
        print(f"❌ 失败: {error_count} 个")
        print(f"⚠️ 警告: {warning_count} 个")
        print(f"📊 生成chunk数: {total_chunks}")
        print(f"🧵 使用线程数: {max_workers}")
        print(f"🌐 API地址: {API_URL}")
        print(f"📁 输出目录: {output_folder}")
        
        if success_count > 0:
            print(f"\n✅ 成功处理的文件:")
            for result in results:
                if result["status"] == "success":
                    thread_info = f"[{result.get('thread_id', 'unknown')}]"
                    print(f"  • {os.path.basename(result['file_path'])} → {result['output_file']} ({result['chunks_count']} 切片) {thread_info}")
        
        if error_count > 0:
            print(f"\n❌ 处理失败的文件:")
            for result in results:
                if result["status"] == "error":
                    thread_info = f"[{result.get('thread_id', 'unknown')}]"
                    print(f"  • {os.path.basename(result['file_path'])} - {result.get('error', '未知错误')} {thread_info}")
        
        print(f"\n💡 说明:")
        print(f"📋 每个文件通过API切分并生成一个JSON文件，包含该文件的所有切片")
        print(f"📊 详细处理日志保存在: processing_report.json")
        print(f"🌐 使用API进行文档切分，支持远程处理")
        
        return output_folder
        
    except Exception as e:
        raise RuntimeError(f"批量处理文件时出错: {str(e)}")


if __name__ == "__main__":
    import sys
    
    # ==================== 配置参数 ====================
    # 在这里直接定义输入文件夹路径
    输入文件夹路径 = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output"  # 修改此路径为你的目标文件夹
    
    # 其他可选参数
    输出文件夹路径 = None     # None表示自动生成，也可以指定具体路径
    标题层级 = 6            # Markdown标题层级
    最大块长度 = 2048       # 每个切片的最大长度
    添加文件名 = True        # 是否在切片中添加文件名
    支持的文件格式 = None  # None表示使用默认的广泛文件格式支持
    并行线程数 = 10          # 并行处理的线程数量
    
    # ==================================================
    
    print("=== API文档切分工具（JSON格式输出）===")
    print(f"输入文件夹: {输入文件夹路径}")
    print(f"输出文件夹: {'自动生成' if 输出文件夹路径 is None else 输出文件夹路径}")
    print(f"API地址: {API_URL}")
    print(f"最大块长度: {最大块长度} 字符")
    print(f"标题层级: {标题层级}")
    print(f"支持格式: {'自动检测多种文本格式' if 支持的文件格式 is None else ', '.join(支持的文件格式)}")
    print(f"并行线程数: {并行线程数} 个")
    
    try:
        result_folder = main_api_chunks(
            input_folder=输入文件夹路径,
            output_folder=输出文件夹路径, 
            headers_level=标题层级,
            chunk_max_len=最大块长度,
            add_filename=添加文件名,
            supported_extensions=支持的文件格式,
            max_workers=并行线程数
        )
        print(f"\n✅ 处理成功完成，结果保存在: {result_folder}")
        print(f"\n💡 使用说明:")
        print(f"1. 每个源文件调用API切分并生成一个对应的JSON文件")
        print(f"2. JSON文件包含该文件的所有切片数组")
        print(f"3. 每个切片包含API返回的内容、元数据等信息")
        print(f"4. 🚀 使用{并行线程数}个线程并行调用API，显著提升处理速度")
        print(f"5. 🌐 API切分结果更准确，支持远程处理")
        
    except Exception as e:
        print(f"\n❌ 处理失败: {str(e)}")
        sys.exit(1)


# 原始测试代码（保留）
def test_single_api_call():
    """测试单个API调用"""
    try:
        response = requests.post(API_URL, headers=API_HEADERS, json=data_example)

        # 检查响应状态码
        response.raise_for_status()

        # 打印响应内容
        result = response.json()
        print("状态码:", response.status_code)
        print("响应内容:")
        print(json.dumps(result, indent=4, ensure_ascii=False))
        
        # 检查响应结构
        if "code" in result:
            code_info = result["code"]
            print(f"\nAPI状态: {code_info.get('msg')} (代码: {code_info.get('code')})")
            
        if "bo" in result and len(result['bo']) > 0:
            file_chunks = result['bo'][0]  # 取第一个文件的chunks
            print(f"切片数量: {len(file_chunks)}")
            for i, chunk_content in enumerate(file_chunks, 1):
                print(f"切片{i}: {chunk_content[:50]}{'...' if len(chunk_content) > 50 else ''}")
        
        print(f"\n📋 输出格式说明:")
        print(f"- processing_method: 'api'")
        print(f"- api_response: 包含完整API响应")
        print(f"- chunk字段: content, metadata, title, chunk_size, supplement, images")
        print(f"- 与本地版本格式完全一致")

    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {e}")


# 如果需要测试单个API调用，可以运行以下代码
# test_single_api_call()