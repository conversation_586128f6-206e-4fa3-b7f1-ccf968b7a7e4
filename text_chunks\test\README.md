# test 切片索引

- 原文件: test.txt
- 切分时间: 2025-08-22 17:12:41
- 总块数: 9
- 最大块长度: 800

## 切片列表

001. **chunk_001.txt** (14 字符)
     预览: # test。## 第1节。...

002. **chunk_002.txt** (750 字符)
     预览: 2024-06-19 14:01:39,974 ERROR /home/<USER>/smartRAG_v3_uat/Retrieve/Retriever.py(line:94) 检索接口报错,错误信息为{...

003. **chunk_003.txt** (153 字符)
     预览: , 'searchWeight': 0.5, 'relatedIndex': 0.9, 'matchNumber': 10, 'question': 'Q:{"users":["00307181","...

004. **chunk_004.txt** (289 字符)
     预览: ', 'searchType': 'vector', 'isRank': True, 'metadataDisplay': True}。2024-06-19 14:01:39,975 INFO /us...

005. **chunk_005.txt** (750 字符)
     预览: 2024-06-19 14:01:40,765 ERROR /home/<USER>/smartRAG_v3_uat/Retrieve/Retriever.py(line:94) 检索接口报错,错误信息为{...

006. **chunk_006.txt** (89 字符)
     预览: , 'searchWeight': 0.5, 'relatedIndex': 0.9, 'matchNumber': 10, 'question': 'Q:许可证物项负责人的职责...

007. **chunk_007.txt** (68 字符)
     预览: ', 'searchType': 'vector', 'isRank': True, 'metadataDisplay': True}。...

008. **chunk_008.txt** (756 字符)
     预览: 检索接口报错,错误信息为{'code': {'code': '0005', 'msgId': 'RetCode.BusinessError', 'msg': 'Business Error'}, 'b...

009. **chunk_009.txt** (68 字符)
     预览: ', 'searchType': 'vector', 'isRank': True, 'metadataDisplay': True}。...

