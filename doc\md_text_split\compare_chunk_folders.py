#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件夹Chunk对比专用工具

专门用于对比两个文件夹中相同文档的chunk结果
适用于API版本和本地版本的输出对比
"""

import os
import sys
from datetime import datetime
from chunk_comparator import ChunkComparator

def compare_specific_folders():
    """对比指定的两个文件夹"""
    
    # 您的具体文件夹路径
    api_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_api_chunks_20250907_170250"
    local_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_json_chunks_20250907_170302"
    
    print("🎯 Chunk文件夹对比工具")
    print("=" * 60)
    print(f"API版本文件夹:   {api_folder}")
    print(f"本地版本文件夹:  {local_folder}")
    print("=" * 60)
    
    # 检查文件夹是否存在
    if not os.path.exists(api_folder):
        print(f"❌ API版本文件夹不存在: {api_folder}")
        return
    
    if not os.path.exists(local_folder):
        print(f"❌ 本地版本文件夹不存在: {local_folder}")
        return
    
    # 生成报告文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"chunk_comparison_report_{timestamp}.txt"
    detailed_file = f"chunk_comparison_detailed_{timestamp}.json"
    
    print(f"📄 报告将保存为: {report_file}")
    print(f"📊 详细数据将保存为: {detailed_file}")
    print()
    
    # 进行对比
    comparator = ChunkComparator()
    result = comparator.batch_compare_folders(api_folder, local_folder, report_file)
    
    if "error" in result:
        print(f"❌ 对比失败: {result['error']}")
        if "details" in result:
            details = result["details"]
            print(f"\n📊 详细信息:")
            print(f"  API文件夹文件数: {details.get('folder1_files', 0)}")
            print(f"  本地文件夹文件数: {details.get('folder2_files', 0)}")
            
            if details.get('only_in_folder1'):
                print(f"  仅在API文件夹: {len(details['only_in_folder1'])} 个")
            if details.get('only_in_folder2'):
                print(f"  仅在本地文件夹: {len(details['only_in_folder2'])} 个")
    else:
        print(f"\n✅ 对比完成!")
        
        # 保存详细结果
        import json
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 显示摘要
        stats = result["summary_stats"]
        print(f"\n📊 对比摘要:")
        print(f"  对比文件数: {stats['total_comparisons']}")
        print(f"  优秀质量: {stats['excellent_count']} 个")
        print(f"  良好质量: {stats['good_count']} 个")
        print(f"  一般质量: {stats['fair_count']} 个")
        print(f"  较差质量: {stats['poor_count']} 个")
        
        avg_score = (stats['avg_coverage_score'] + stats['avg_similarity_score'] + 
                    stats['avg_size_consistency'] + stats['avg_chunk_count_consistency']) / 4
        print(f"\n🎯 平均综合得分: {avg_score:.3f}")
        
        if avg_score > 0.9:
            print("🎉 两种方法的切分结果一致性优秀！")
        elif avg_score > 0.7:
            print("👍 两种方法的切分结果一致性良好")
        elif avg_score > 0.5:
            print("⚠️ 两种方法的切分结果存在一定差异")
        else:
            print("❌ 两种方法的切分结果差异较大，需要深入分析")

def compare_custom_folders():
    """对比用户指定的文件夹"""
    
    if len(sys.argv) < 3:
        print("使用方法: python compare_chunk_folders.py <api_folder> <local_folder>")
        print("示例: python compare_chunk_folders.py ./api_output ./local_output")
        return
    
    api_folder = sys.argv[1]
    local_folder = sys.argv[2]
    
    print("🎯 自定义文件夹对比")
    print("=" * 60)
    print(f"API版本文件夹:   {api_folder}")
    print(f"本地版本文件夹:  {local_folder}")
    print("=" * 60)
    
    # 生成报告文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"chunk_comparison_report_{timestamp}.txt"
    
    # 进行对比
    comparator = ChunkComparator()
    result = comparator.batch_compare_folders(api_folder, local_folder, report_file)
    
    if "error" in result:
        print(f"❌ 对比失败: {result['error']}")
    else:
        print(f"✅ 对比完成! 报告已保存: {report_file}")

def show_folder_info(folder_path):
    """显示文件夹信息"""
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
    
    chunk_files = [f for f in os.listdir(folder_path) if f.endswith('_chunks.json')]
    
    print(f"📁 文件夹: {folder_path}")
    print(f"📊 chunk文件数: {len(chunk_files)}")
    
    if chunk_files:
        print(f"📋 文件列表 (前10个):")
        for i, file in enumerate(sorted(chunk_files)[:10], 1):
            print(f"  {i:2d}. {file}")
        
        if len(chunk_files) > 10:
            print(f"     ... 还有 {len(chunk_files) - 10} 个文件")
    
    return len(chunk_files)

def quick_analysis():
    """快速分析两个文件夹"""
    
    # 您的具体文件夹路径
    api_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_api_chunks_20250907_170250"
    local_folder = r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document\parsed_output_json_chunks_20250907_170302"
    
    print("🔍 快速文件夹分析")
    print("=" * 60)
    
    api_count = show_folder_info(api_folder)
    print()
    local_count = show_folder_info(local_folder)
    
    print("\n📊 初步分析:")
    if api_count and local_count:
        print(f"  可能的对比文件对: {min(api_count, local_count)} 个")
        print(f"  文件数量差异: {abs(api_count - local_count)} 个")
        
        if api_count == local_count:
            print("  ✅ 两个文件夹文件数量相同")
        else:
            print("  ⚠️ 两个文件夹文件数量不同，可能存在缺失文件")
    
    print(f"\n💡 建议运行完整对比:")
    print(f"  python compare_chunk_folders.py")

def main():
    """主函数"""
    
    if len(sys.argv) == 1:
        # 默认对比指定文件夹
        compare_specific_folders()
    elif len(sys.argv) == 2 and sys.argv[1] == "analyze":
        # 快速分析
        quick_analysis()
    elif len(sys.argv) >= 3:
        # 对比用户指定的文件夹
        compare_custom_folders()
    else:
        print("🎯 Chunk文件夹对比工具")
        print("=" * 40)
        print("使用方法:")
        print("  1. 对比默认文件夹:")
        print("     python compare_chunk_folders.py")
        print()
        print("  2. 快速分析文件夹:")
        print("     python compare_chunk_folders.py analyze")
        print()
        print("  3. 对比自定义文件夹:")
        print("     python compare_chunk_folders.py <api_folder> <local_folder>")
        print()
        print("示例:")
        print("  python compare_chunk_folders.py ./api_output ./local_output")

if __name__ == "__main__":
    main()
