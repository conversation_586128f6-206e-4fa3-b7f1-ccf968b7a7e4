# 两个脚本输出格式统一说明

## 📋 背景
为了保持API版本（`split_api_test.py`）和本地版本（`text_split.py`）的输出格式一致，便于后续统一处理，对两个脚本的输出格式进行了统一。

## 🔍 统一前的差异

### API版本（split_api_test.py）
```json
{
  "source_file": "文件名.txt",
  "source_path": "文件路径",
  "processing_time": "时间",
  "api_response": {...},
  "settings": {
    "headers_level": 6,
    "chunk_max_len": 2048,
    "add_filename": true
  },
  "total_chunks": 3,
  "chunks": [
    {
      "chunk_id": 1,
      "content": "内容",
      "metadata": {},
      "title": "",
      "chunk_size": 58
    }
  ]
}
```

### 本地版本（text_split.py）
```json
{
  "source_file": "文件名.txt",
  "source_path": "文件路径",
  "processing_time": "时间",
  "settings": {
    "headers_level": 6,
    "max_chunk_len": 800
  },
  "total_chunks": 3,
  "chunks": [
    {
      "chunk_id": 1,
      "content": "内容",
      "metadata": {},
      "supplement": "",
      "images": []
    }
  ]
}
```

## 🔧 统一后的格式

### 统一的JSON结构
```json
{
  "source_file": "文件名.txt",
  "source_path": "文件路径",
  "processing_time": "2025-01-03T10:00:00",
  "processing_method": "api|local",
  "api_response": {...} | null,
  "settings": {
    "headers_level": 6,
    "chunk_max_len": 2048,
    "add_filename": true
  },
  "total_chunks": 3,
  "chunks": [
    {
      "chunk_id": 1,
      "content": "内容",
      "metadata": {},
      "title": "",
      "chunk_size": 58,
      "supplement": "",
      "images": []
    }
  ]
}
```

## 🎯 统一规则

### 字段映射表

| 字段分类 | 字段名 | API版本 | 本地版本 | 统一后 |
|---------|--------|---------|----------|---------|
| **顶级字段** | `processing_method` | ❌ | ❌ | ✅ "api" / "local" |
| | `api_response` | ✅ | ❌ | ✅ API响应 / null |
| | `settings.chunk_max_len` | ✅ | ❌ (max_chunk_len) | ✅ 统一命名 |
| | `settings.add_filename` | ✅ | ❌ | ✅ API有 / 本地默认true |
| **chunk字段** | `title` | ✅ | ❌ | ✅ API有 / 本地为空 |
| | `chunk_size` | ✅ | ❌ | ✅ 都计算内容长度 |
| | `supplement` | ❌ | ✅ | ✅ API为空 / 本地有值 |
| | `images` | ❌ | ✅ | ✅ API为空数组 / 本地有图片 |

### 字段填充策略

#### API版本补充的字段
- `processing_method`: 固定为 "api"
- `supplement`: 固定为空字符串 ""
- `images`: 固定为空数组 []

#### 本地版本补充的字段
- `processing_method`: 固定为 "local"
- `api_response`: 固定为 null
- `settings.chunk_max_len`: 原 `max_chunk_len` 重命名
- `settings.add_filename`: 固定为 true
- `title`: 固定为空字符串 ""
- `chunk_size`: 计算 `content` 长度

## ✅ 统一的优势

### 1. 后续处理一致性
```python
# 现在可以用统一的代码处理两种输出
def process_chunks(json_data):
    method = json_data["processing_method"]
    chunks = json_data["chunks"]
    
    for chunk in chunks:
        content = chunk["content"]
        size = chunk["chunk_size"]
        images = chunk["images"]  # 两种格式都有
        supplement = chunk["supplement"]  # 两种格式都有
        # 统一处理逻辑...
```

### 2. 字段完整性
- 所有输出JSON都包含完整的字段集合
- 不用担心字段缺失导致的处理错误
- 便于数据库存储（固定schema）

### 3. 可扩展性
- 新增字段时，两个脚本同步添加
- 保持格式的向前兼容性
- 便于功能合并和重构

## 🔄 向后兼容性

### 现有代码兼容
- 原有字段保持不变
- 只是新增了缺失的字段
- 不会破坏现有的处理逻辑

### 自动识别
```python
# 可以通过processing_method字段识别处理方式
def get_processing_info(json_data):
    if json_data["processing_method"] == "api":
        return json_data["api_response"]
    else:
        return "本地处理，无API响应"
```

## 📊 使用示例

### 统一的数据处理
```python
import json

def analyze_chunks(json_file):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统一访问所有字段
    total = data["total_chunks"]
    method = data["processing_method"]
    
    for chunk in data["chunks"]:
        print(f"Chunk {chunk['chunk_id']}: {chunk['chunk_size']} chars")
        if chunk["images"]:
            print(f"  包含 {len(chunk['images'])} 张图片")
        if chunk["supplement"]:
            print(f"  补充信息: {chunk['supplement']}")
    
    return f"处理方式: {method}, 总计: {total} 个切片"
```

## 🚀 未来规划

### 功能合并
- 可以考虑将两个脚本合并为一个
- 通过参数选择处理方式（API或本地）
- 统一的配置和参数管理

### 标准化扩展
- 建立标准的chunk格式规范
- 支持更多的元数据字段
- 兼容其他切分工具的输出

## ⚠️ 注意事项

1. **字段语义**: 
   - `supplement` 和 `images` 在API版本中为空是正常的
   - `api_response` 在本地版本中为null是预期的

2. **数据大小**:
   - `api_response` 可能包含较大的原始响应数据
   - 本地版本的 `images` 数组可能包含图片路径信息

3. **处理性能**:
   - 统一格式后JSON文件可能略大
   - 但提升了处理的一致性和便利性
