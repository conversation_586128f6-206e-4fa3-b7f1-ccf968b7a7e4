#!/usr/bin/env python3
"""
找出不支持的文件类型
"""

from pathlib import Path

def find_unsupported_files():
    """找出不支持的文件"""
    
    source_dir = Path(r"D:\JAVA\workspace\AI\ZTE_AI\product_doc_build\doc\origin_document")
    
    # 支持的文件扩展名
    supported_extensions = {'.pdf', '.docx', '.doc'}
    
    print("=== 查找不支持的文件 ===")
    print(f"源目录: {source_dir}")
    
    if not source_dir.exists():
        print(f"❌ 目录不存在: {source_dir}")
        return
    
    supported_files = []
    unsupported_files = []
    
    for file_path in source_dir.iterdir():
        if file_path.is_file():
            if file_path.suffix.lower() in supported_extensions:
                supported_files.append(file_path)
            else:
                unsupported_files.append(file_path)
    
    print(f"\n📊 文件分类:")
    print(f"支持的文件: {len(supported_files)} 个")
    print(f"不支持的文件: {len(unsupported_files)} 个")
    
    if unsupported_files:
        print(f"\n🚫 不支持的文件列表:")
        for file_path in unsupported_files:
            size = file_path.stat().st_size
            size_kb = size / 1024
            print(f"  • {file_path.name}")
            print(f"    类型: {file_path.suffix if file_path.suffix else '无扩展名'}")
            print(f"    大小: {size_kb:.1f} KB")
            
            # 尝试读取文件头来判断真实类型
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(10)
                    if header.startswith(b'%PDF'):
                        print(f"    实际类型: PDF文件（可能扩展名错误）")
                    elif header.startswith(b'PK'):
                        print(f"    实际类型: Office文档（ZIP格式）")
                    elif header.startswith(b'\xd0\xcf\x11\xe0'):
                        print(f"    实际类型: 旧版Office文档")
                    else:
                        print(f"    文件头: {header.hex()}")
            except Exception as e:
                print(f"    无法读取文件: {e}")
            print()
    else:
        print("✅ 所有文件都是支持的格式")

if __name__ == "__main__":
    find_unsupported_files()
