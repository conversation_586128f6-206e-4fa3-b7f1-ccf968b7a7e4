import requests
import json

url = "https://llm.dt.zte.com.cn/zte-ibo-acm-tools/cut"

headers = {
    'Content-Type': 'application/json'
}


content = '''

关键信息：

1）市场持续下滑，国内集采丢标，海外粮仓市场不断被突破替换。

2）核心芯片多数外购，无法自研迭代，不具备客户长期服务能力，无法应对融合演进场景。

3）产品/研发/工程竞争力全面落后。

烽火已陷入经营不善，产品滞后，市场萎缩的负循环

# 经营不善，市场份额持续下降

市场占比持续走低，沦为二流厂商：根据知名咨询公司Omdia的报告显示，烽火光网络产品，全球市场占有率仅6%（除去PTN份额纯WMD占比仅4%），排名跌出前5。

国际市场规模市场少，整体市场占比低：烽火OTN销售国内市场为主，海外市场占比低于15%。国际商用案例较少，呈零星布局，无国际大T规模商用案例。存量市场持续萎缩（如马来TM、泰国3BB份额下降、完全退出印度市场等）。

国内市场不均衡，中移骨干网出局：烽火国内市场份额主要集中在少数省份，盈利集中在中国移动。移动国干十三期集采份额为0，国干份额几乎萎缩殆尽（仅剩8%），同时2022联通集采也无份额，国内市场持续萎缩。

# 关键芯片无法自研，不具备核心竞争力

核心芯片无法灵活迭代，无客户长期服务能力：烽火近日宣称已实现核心器件的自主研发，包括DSP、Framer和光收发器件的设计和生产能力，但是鉴于其前期无自研能力，推测为其夸大宣传。如果业界最后一家商业Framer厂家Microchip退出，烽火将至少面临三年无Framer可用的窘境，无法实现长期稳定客户服务。

无芯片融合演进能力：芯片组合应用可拓展产品应用范围，提升产品竞争力。烽火无包括NP芯片在内的IP芯片自研能力，多厂家外购芯片无法集成（中兴自研集成芯片可完成烽火至少四个商用芯片功能），无法快速满足未来设备融合的趋势。

# 产品/研发/服务全面落后

产品平台与主流厂商存在代差：核心硬件依赖外采，软件积累不足，与主流厂商差距明显，产品已经落后主流厂商一代。

a）核心芯片及关键光器件依赖外采：商用芯片主要依赖外采，线路单板端口较主流厂商低20%-50%；关键频谱扩展光器件外采渠道受限，C+L最大系统容量较主流厂商低20%；集成度及器件限制，大容量高速光背板OXC平台无规模发货。

b）软件积累及定制化开发能力不足：烽火为传统光网厂家仅涉及传输相关领域，无核心网、无线、路由器等长期软件开发积累，SDN相关智能化功能仅能满足国内运营商基本功能，无SDN端到端部署能力；无海外SDN部署案例，权威SDN评测组织GlobalData报告中无排名。

研发能力下降，自研及定制化能力落后：关键岗位研发人员流失率高，严重影响设备功能开发进度；研发跟随国内运营商需求为主，海外客户定制化能力匮乏。

售后交付服务能力无法匹配客户持续发展需求：海外项目工程问题多发，故障处理时间长；设备运行不稳定，给客户造成了重大损失，影响恶劣。无法匹配客户持续发展需求。

设备运行故障率高，无规律，导致客户骨干网络长时间断网，如印度BSNL，PGCIL。

过度承诺导致交付产品能力达不到承诺指标，损害客户利益。如中国电信国干华南环，因WASON功能不达标，全网更换主控板。

（3）海外市场发货问题导致全网逐站断电升级，造成客户大量二次支出。如泰国3BB PDU闪断，导致全网更换PDU；马来TM光传输子框无法兼容100G需更换新子框，全网更换子框。

# 四、小结

随着技术的不断演进及主流厂商逐渐实现关键器件自研，烽火采用外购商业套片不仅在性能和功耗上全面落后，且其供货能力及持续演进能力也受各种因素制约。目前烽火已经逐渐陷入“经营日况愈下->研发投入持续下降->产品方案性能无法改善，难以提升商用->运营商失去信心不断被抛弃->经营无法改善”的负循环中，短期内都无法改善和解决。

'''

data = {
    "file_texts": [
        {
            "title": "ecp八要素",
            "content": content
        },
        {
            "title": "eccn",
            "content": "eccn是..."
        }
    ],
    "headers_level": 4,
    "chunk_max_len": 800,
    "add_filename": True
}

try:
    response = requests.post(url, headers=headers, json=data)

    # 检查响应状态码
    response.raise_for_status()

    # 打印响应内容
    print("状态码:", response.status_code)
    print("响应内容:")
    print(json.dumps(response.json(), indent=4, ensure_ascii=False))

except requests.exceptions.RequestException as e:
    print(f"请求发生错误: {e}")