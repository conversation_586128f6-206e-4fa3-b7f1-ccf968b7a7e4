"""
三路检索监控分析器
用于分析ES、<PERSON><PERSON><PERSON><PERSON>、K<PERSON>三路检索的排序贡献度和效果评估
"""

import json
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

@dataclass
class RetrievalItem:
    """检索项数据类"""
    index: int  # 在原始检索结果中的序号
    source: str  # 来源路径 (ES/Milvus/KG)
    content: str  # 内容
    score: float  # 原始得分
    final_rank: Optional[int] = None  # 最终排序位置
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class RetrievalStats:
    """检索统计数据类"""
    total_count: int  # 总数量
    top_10_count: int  # 前10名数量
    top_20_count: int  # 前20名数量
    avg_rank: float  # 平均排名
    contribution_rate: float  # 贡献率
    effectiveness_score: float  # 有效性得分


class RetrievalMonitor:
    """三路检索监控分析器"""
    
    def __init__(self, query_id: str = None, query_text: str = ""):
        """
        初始化监控器
        
        Args:
            query_id: 查询ID
            query_text: 查询文本
        """
        self.query_id = query_id or f"Q{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.query_text = query_text
        self.start_time = datetime.now()
        
        # 存储各路检索结果
        self.es_results: List[RetrievalItem] = []
        self.milvus_results: List[RetrievalItem] = []
        self.kg_results: List[RetrievalItem] = []
        
        # 存储最终排序结果
        self.final_results: List[RetrievalItem] = []
        
        # 统计数据
        self.stats: Dict[str, RetrievalStats] = {}
        
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(f"RetrievalMonitor_{self.query_id}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s %(levelname)s: [监控] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def add_es_results(self, results: List[Dict[str, Any]]) -> None:
        """
        添加ES检索结果
        
        Args:
            results: ES检索结果列表，每个元素包含content和score
        """
        self.es_results = []
        for i, result in enumerate(results):
            item = RetrievalItem(
                index=i + 1,
                source="ES",
                content=result.get('content', ''),
                score=result.get('score', 0.0)
            )
            self.es_results.append(item)
        
        self.logger.info(f"ES检索结果已记录 - 数量: {len(self.es_results)}条")
    
    def add_milvus_results(self, results: List[Dict[str, Any]]) -> None:
        """
        添加Milvus检索结果
        
        Args:
            results: Milvus检索结果列表
        """
        self.milvus_results = []
        for i, result in enumerate(results):
            item = RetrievalItem(
                index=i + 1,
                source="Milvus",
                content=result.get('content', ''),
                score=result.get('score', 0.0)
            )
            self.milvus_results.append(item)
        
        self.logger.info(f"Milvus检索结果已记录 - 数量: {len(self.milvus_results)}条")
    
    def add_kg_results(self, results: List[Dict[str, Any]]) -> None:
        """
        添加KG检索结果
        
        Args:
            results: KG检索结果列表
        """
        self.kg_results = []
        for i, result in enumerate(results):
            item = RetrievalItem(
                index=i + 1,
                source="KG",
                content=result.get('content', ''),
                score=result.get('score', 0.0)
            )
            self.kg_results.append(item)
        
        self.logger.info(f"KG检索结果已记录 - 数量: {len(self.kg_results)}条")
    
    def set_final_results(self, final_results: List[Dict[str, Any]]) -> None:
        """
        设置最终重排序结果
        
        Args:
            final_results: 最终排序结果列表
        """
        self.final_results = []
        
        # 建立内容到原始结果的映射
        content_to_source = {}
        
        # 添加所有原始结果的映射
        for item in self.es_results + self.milvus_results + self.kg_results:
            content_key = item.content.strip()[:100]  # 使用前100字符作为匹配键
            if content_key not in content_to_source:
                content_to_source[content_key] = []
            content_to_source[content_key].append(item)
        
        # 匹配最终结果到原始来源
        for rank, result in enumerate(final_results, 1):
            content = result.get('content', '')
            content_key = content.strip()[:100]
            
            # 寻找匹配的原始结果
            matched_item = None
            if content_key in content_to_source:
                # 取第一个匹配的项
                original_item = content_to_source[content_key].pop(0)
                matched_item = RetrievalItem(
                    index=original_item.index,
                    source=original_item.source,
                    content=content,
                    score=result.get('score', original_item.score),
                    final_rank=rank
                )
                
                # 如果该内容已经用完，从映射中删除
                if not content_to_source[content_key]:
                    del content_to_source[content_key]
            
            if matched_item:
                self.final_results.append(matched_item)
            else:
                # 如果没有匹配到，创建一个未知来源的项
                self.final_results.append(RetrievalItem(
                    index=0,
                    source="Unknown",
                    content=content,
                    score=result.get('score', 0.0),
                    final_rank=rank
                ))
        
        self.logger.info(f"最终排序结果已记录 - 数量: {len(self.final_results)}条")
        
        # 计算统计数据
        self._calculate_stats()
    
    def _calculate_stats(self) -> None:
        """计算各路检索的统计数据"""
        sources = ["ES", "Milvus", "KG"]
        
        for source in sources:
            # 获取该来源在最终结果中的项
            source_items = [item for item in self.final_results if item.source == source]
            
            if not source_items:
                self.stats[source] = RetrievalStats(0, 0, 0, 0, 0, 0)
                continue
            
            total_count = len(source_items)
            top_10_count = len([item for item in source_items if item.final_rank and item.final_rank <= 10])
            top_20_count = len([item for item in source_items if item.final_rank and item.final_rank <= 20])
            
            # 计算平均排名
            ranks = [item.final_rank for item in source_items if item.final_rank]
            avg_rank = sum(ranks) / len(ranks) if ranks else float('inf')
            
            # 计算贡献率（在最终结果中的占比）
            contribution_rate = total_count / len(self.final_results) if self.final_results else 0
            
            # 计算有效性得分（综合考虑数量和排名质量）
            # 有效性得分 = (前10名数量 * 2 + 前20名数量) / 总数量 * 贡献率
            if total_count > 0:
                quality_score = (top_10_count * 2 + top_20_count) / total_count
                effectiveness_score = quality_score * contribution_rate
            else:
                effectiveness_score = 0
            
            self.stats[source] = RetrievalStats(
                total_count=total_count,
                top_10_count=top_10_count,
                top_20_count=top_20_count,
                avg_rank=avg_rank,
                contribution_rate=contribution_rate,
                effectiveness_score=effectiveness_score
            )
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """
        生成分析报告
        
        Returns:
            包含详细分析结果的字典
        """
        end_time = datetime.now()
        response_time = (end_time - self.start_time).total_seconds()
        
        report = {
            "query_info": {
                "query_id": self.query_id,
                "query_text": self.query_text,
                "response_time": f"{response_time:.3f}s",
                "timestamp": end_time.strftime('%Y-%m-%d %H:%M:%S')
            },
            "retrieval_counts": {
                "ES": len(self.es_results),
                "Milvus": len(self.milvus_results),
                "KG": len(self.kg_results),
                "final_total": len(self.final_results)
            },
            "source_analysis": {},
            "ranking_details": [],
            "effectiveness_ranking": []
        }
        
        # 各来源统计分析
        for source, stats in self.stats.items():
            report["source_analysis"][source] = {
                "总数量": stats.total_count,
                "前10名数量": stats.top_10_count,
                "前20名数量": stats.top_20_count,
                "平均排名": round(stats.avg_rank, 2) if stats.avg_rank != float('inf') else "无",
                "贡献率": f"{stats.contribution_rate:.2%}",
                "有效性得分": round(stats.effectiveness_score, 4)
            }
        
        # 排序详情
        for item in self.final_results:
            report["ranking_details"].append({
                "最终排名": item.final_rank,
                "来源": item.source,
                "原始序号": item.index,
                "得分": item.score,
                "内容预览": item.content[:50] + "..." if len(item.content) > 50 else item.content
            })
        
        # 有效性排行
        effectiveness_list = [(source, stats.effectiveness_score) for source, stats in self.stats.items()]
        effectiveness_list.sort(key=lambda x: x[1], reverse=True)
        
        for i, (source, score) in enumerate(effectiveness_list, 1):
            report["effectiveness_ranking"].append({
                "排名": i,
                "检索路径": source,
                "有效性得分": round(score, 4),
                "评级": self._get_effectiveness_grade(score)
            })
        
        return report
    
    def _get_effectiveness_grade(self, score: float) -> str:
        """根据有效性得分给出评级"""
        if score >= 0.3:
            return "优秀"
        elif score >= 0.2:
            return "良好" 
        elif score >= 0.1:
            return "一般"
        else:
            return "较差"
    
    def print_analysis_report(self) -> None:
        """打印分析报告"""
        report = self.generate_analysis_report()
        
        print("\n" + "="*80)
        print("           三路检索效果分析报告")
        print("="*80)
        
        # 查询信息
        query_info = report["query_info"]
        print(f"\n📋 查询信息:")
        print(f"   查询ID: {query_info['query_id']}")
        print(f"   查询内容: {query_info['query_text']}")
        print(f"   响应时间: {query_info['response_time']}")
        print(f"   分析时间: {query_info['timestamp']}")
        
        # 检索数量统计
        counts = report["retrieval_counts"]
        print(f"\n📊 检索数量统计:")
        print(f"   ES: {counts['ES']}条")
        print(f"   Milvus: {counts['Milvus']}条") 
        print(f"   KG: {counts['KG']}条")
        print(f"   重排序后: {counts['final_total']}条")
        
        # 各来源详细分析
        print(f"\n🔍 各来源详细分析:")
        analysis = report["source_analysis"]
        for source in ["ES", "Milvus", "KG"]:
            if source in analysis:
                stats = analysis[source]
                print(f"   {source}:")
                print(f"     ├─ 总数量: {stats['总数量']}")
                print(f"     ├─ 前10名: {stats['前10名数量']}")
                print(f"     ├─ 前20名: {stats['前20名数量']}")
                print(f"     ├─ 平均排名: {stats['平均排名']}")
                print(f"     ├─ 贡献率: {stats['贡献率']}")
                print(f"     └─ 有效性得分: {stats['有效性得分']}")
        
        # 有效性排行
        print(f"\n🏆 检索效果排行榜:")
        for rank_info in report["effectiveness_ranking"]:
            rank = rank_info["排名"]
            source = rank_info["检索路径"]
            score = rank_info["有效性得分"]
            grade = rank_info["评级"]
            
            medal = "🥇" if rank == 1 else "🥈" if rank == 2 else "🥉" if rank == 3 else "  "
            print(f"   {medal} 第{rank}名: {source} (得分: {score}, 评级: {grade})")
        
        # 排序分布概览
        print(f"\n📈 排序分布概览:")
        self._print_ranking_distribution()
        
        print("\n" + "="*80)
    
    def _print_ranking_distribution(self) -> None:
        """打印排序分布概览"""
        # 统计各来源在不同排名段的分布
        ranges = [(1, 10), (11, 20), (21, 30), (31, float('inf'))]
        range_names = ["1-10名", "11-20名", "21-30名", "30名后"]
        
        for source in ["ES", "Milvus", "KG"]:
            source_items = [item for item in self.final_results if item.source == source]
            if not source_items:
                continue
                
            print(f"   {source}分布:", end=" ")
            for i, (start, end) in enumerate(ranges):
                count = len([item for item in source_items 
                           if item.final_rank and start <= item.final_rank <= end])
                if count > 0:
                    print(f"{range_names[i]}({count})", end=" ")
            print()
    
    def save_report_to_file(self, filename: str = None) -> str:
        """
        保存报告到文件
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = f"retrieval_analysis_{self.query_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = self.generate_analysis_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"分析报告已保存到文件: {filename}")
        return filename
    
    def create_visualization(self, save_path: str = None) -> str:
        """
        创建可视化图表
        
        Args:
            save_path: 保存路径，如果为None则自动生成
            
        Returns:
            保存的图片路径
        """
        if save_path is None:
            save_path = f"retrieval_analysis_{self.query_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'三路检索效果分析 - {self.query_id}', fontsize=16, fontweight='bold')
        
        # 1. 贡献率饼图
        sources = list(self.stats.keys())
        contribution_rates = [self.stats[source].contribution_rate for source in sources]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        ax1.pie(contribution_rates, labels=sources, autopct='%1.1f%%', colors=colors[:len(sources)])
        ax1.set_title('各路检索贡献率分布')
        
        # 2. 有效性得分对比
        effectiveness_scores = [self.stats[source].effectiveness_score for source in sources]
        bars = ax2.bar(sources, effectiveness_scores, color=colors[:len(sources)])
        ax2.set_title('有效性得分对比')
        ax2.set_ylabel('有效性得分')
        
        # 在柱状图上添加数值标签
        for bar, score in zip(bars, effectiveness_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')
        
        # 3. 排名分布热力图
        rank_ranges = ["1-10名", "11-20名", "21-30名", "30名后"]
        rank_data = []
        
        for source in sources:
            source_items = [item for item in self.final_results if item.source == source]
            row = []
            ranges = [(1, 10), (11, 20), (21, 30), (31, float('inf'))]
            
            for start, end in ranges:
                count = len([item for item in source_items 
                           if item.final_rank and start <= item.final_rank <= end])
                row.append(count)
            rank_data.append(row)
        
        im = ax3.imshow(rank_data, cmap='YlOrRd', aspect='auto')
        ax3.set_xticks(range(len(rank_ranges)))
        ax3.set_xticklabels(rank_ranges)
        ax3.set_yticks(range(len(sources)))
        ax3.set_yticklabels(sources)
        ax3.set_title('排名分布热力图')
        
        # 在热力图上添加数值
        for i in range(len(sources)):
            for j in range(len(rank_ranges)):
                ax3.text(j, i, str(rank_data[i][j]), ha='center', va='center')
        
        # 4. 综合评估雷达图
        categories = ['总数量', '前10名占比', '前20名占比', '有效性得分']
        
        # 准备雷达图数据
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        ax4 = plt.subplot(2, 2, 4, projection='polar')
        
        for i, source in enumerate(sources):
            stats = self.stats[source]
            # 归一化数据到0-1范围
            values = [
                stats.total_count / max(30, max([self.stats[s].total_count for s in sources])),  # 总数量
                stats.top_10_count / max(1, stats.total_count) if stats.total_count > 0 else 0,  # 前10名占比
                stats.top_20_count / max(1, stats.total_count) if stats.total_count > 0 else 0,  # 前20名占比
                stats.effectiveness_score / max(0.1, max([self.stats[s].effectiveness_score for s in sources]))  # 有效性得分
            ]
            values += values[:1]  # 闭合图形
            
            ax4.plot(angles, values, 'o-', linewidth=2, label=source, color=colors[i])
            ax4.fill(angles, values, alpha=0.25, color=colors[i])
        
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(categories)
        ax4.set_ylim(0, 1)
        ax4.set_title('综合评估雷达图')
        ax4.legend()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"可视化图表已保存到: {save_path}")
        return save_path


def parse_monitor_log(log_text: str) -> Dict[str, Any]:
    """
    解析监控日志文本
    
    Args:
        log_text: 监控日志文本
        
    Returns:
        解析出的监控数据
    """
    lines = log_text.strip().split('\n')
    
    # 提取查询信息
    query_id = None
    query_text = ""
    response_time = ""
    
    # 提取检索数量信息
    es_count = 0
    milvus_count = 0
    kg_count = 0
    final_count = 0
    
    for line in lines:
        if "FAQ查询已记录" in line and "ID:" in line:
            # 提取查询ID
            if "ID:" in line:
                parts = line.split("ID:")
                if len(parts) > 1:
                    id_part = parts[1].split(",")[0].strip()
                    query_id = id_part
            
            # 提取查询内容
            if "查询:" in line:
                parts = line.split("查询:")
                if len(parts) > 1:
                    query_part = parts[1].split(",")[0].strip()
                    query_text = query_part
            
            # 提取响应时间
            if "响应时间:" in line:
                parts = line.split("响应时间:")
                if len(parts) > 1:
                    time_part = parts[1].strip()
                    response_time = time_part
        
        elif "ES:" in line and "Milvus:" in line and "KG:" in line:
            # 解析检索数量
            import re
            es_match = re.search(r'ES:\s*(\d+)', line)
            milvus_match = re.search(r'Milvus:\s*(\d+)', line)
            kg_match = re.search(r'KG:\s*(\d+)', line)
            final_match = re.search(r'重排序:\s*(\d+)', line)
            
            if es_match:
                es_count = int(es_match.group(1))
            if milvus_match:
                milvus_count = int(milvus_match.group(1))
            if kg_match:
                kg_count = int(kg_match.group(1))
            if final_match:
                final_count = int(final_match.group(1))
    
    return {
        "query_id": query_id,
        "query_text": query_text,
        "response_time": response_time,
        "es_count": es_count,
        "milvus_count": milvus_count,
        "kg_count": kg_count,
        "final_count": final_count
    }


# 示例使用函数
def demo_with_log_data():
    """使用日志数据进行演示"""
    
    # 解析日志
    log_text = """
2025-08-28 20:35:19 INFO: [监控] FAQ查询已记录 - ID: Q000038, 查询: M2000-8S支持哪些接口..., 响应时间: 3.229s
2025-08-28 20:35:19 INFO: [监控] FAQ查询已记录 - ID: Q000039, 查询: M2000-8S支持哪些接口..., 响应时间: 3.297s
2025-08-28 20:35:19 INFO: [监控] ES: 20条, Milvus: 20条, KG: 1条, 重排序: 36条
    """
    
    log_data = parse_monitor_log(log_text)
    
    # 创建监控器
    monitor = RetrievalMonitor(
        query_id=log_data["query_id"] or "Q000038",
        query_text=log_data["query_text"]
    )
    
    # 模拟ES检索结果（20条）
    es_results = []
    for i in range(log_data["es_count"]):
        es_results.append({
            "content": f"ES检索结果{i+1}: M2000-8S接口相关内容...",
            "score": 0.9 - i * 0.03
        })
    monitor.add_es_results(es_results)
    
    # 模拟Milvus检索结果（20条）
    milvus_results = []
    for i in range(log_data["milvus_count"]):
        milvus_results.append({
            "content": f"Milvus检索结果{i+1}: M2000-8S接口技术文档...",
            "score": 0.85 - i * 0.025
        })
    monitor.add_milvus_results(milvus_results)
    
    # 模拟KG检索结果（1条）
    kg_results = []
    for i in range(log_data["kg_count"]):
        kg_results.append({
            "content": f"KG检索结果{i+1}: M2000-8S设备接口知识图谱数据...",
            "score": 0.95
        })
    monitor.add_kg_results(kg_results)
    
    # 模拟最终重排序结果（36条）
    final_results = []
    # 前10名：KG(1) + ES(5) + Milvus(4)
    final_results.extend([kg_results[0]])  # KG第1名
    final_results.extend(es_results[:5])   # ES前5名
    final_results.extend(milvus_results[:4])  # Milvus前4名
    
    # 11-20名：ES(6) + Milvus(4)
    final_results.extend(es_results[5:11])
    final_results.extend(milvus_results[4:8])
    
    # 21-36名：剩余的ES和Milvus结果
    final_results.extend(es_results[11:20])
    final_results.extend(milvus_results[8:20])
    
    monitor.set_final_results(final_results)
    
    return monitor


if __name__ == "__main__":
    # 运行演示
    print("🚀 开始三路检索监控分析演示...")
    
    monitor = demo_with_log_data()
    
    # 打印分析报告
    monitor.print_analysis_report()
    
    # 保存报告
    report_file = monitor.save_report_to_file()
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 创建可视化图表
    try:
        chart_file = monitor.create_visualization()
        print(f"📊 可视化图表已保存到: {chart_file}")
    except Exception as e:
        print(f"⚠️  可视化图表生成失败: {e}")
    
    print("\n✅ 演示完成！")

