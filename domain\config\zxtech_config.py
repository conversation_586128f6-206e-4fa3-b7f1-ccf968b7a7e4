import sys
from utils.encryption_util import decrypt
from utils.load_config import load_config_from_net, load_config

# def deal_config(config):
#     key_flag=config['key-flag']
#     llm_secret_en=config['LLM']['llm_headers']['Authorization']
#     es_secret_en=config['Knowledge']['access_secret']
#     es_access_key=config['Knowledge']['access_key']
#     config['LLM']['llm_headers']['Authorization']=decrypt(llm_secret_en,key_flag)
#     config['Knowledge']['access_secret']=decrypt(es_secret_en,key_flag)
#     config['Knowledge']['access_key'] = decrypt(es_access_key, key_flag)
#     return config
#
# config = deal_config(load_config_from_net())

# 原始测试环境配置
# config = {'MicroserviceName': '/zte-ibo-acm-productretrieve', 'BaseUrl': 'https://llm.test.zte.com.cn', 'APP_NAME': 'XboIAdkoPGr6dhU6j0vFyr1E+2sAO5WDSsqofqMjHpnncQoGh2s7EtueLkNQFcA5MAP+xEYKWkJ5/LqNCT8+e5xwMFwIfd2+1uTSJmVYdPCJTWhCE4D2TtMwxKWAEgLmKhbpccuKICd3Mlzwd42P6+L7ATSoykF417GAMkXz7whRKhpzj+OY9fOb6Lh1eEJn0Lhuw3PSFcvHuOh7ys4bd/swF0OihCQd2dduUHaKytYHyBNpiDulsdznOI1vHgNEauZ4LHPuEVzQXE8M36rpTmrIlcq8P1GrkTVlDx9D9wQv/5620U3eSDs2QsDdt0IsdLObcbItTdRRpc8HGVuM8tykauSOUHPQ', 'USER_ID': 'rT13EAE/KqhyYCXaT7M1UIGvRozZw44kixI4yeL52evg6i19Tc37M8unKlRLShUjM5HRZUeJfEfW6pasVoSzb7XBTOJ50b/qFCYkhMpu8W+jZWPH4PENDrmF/m9ahTbtC2wLpJ9FBytZAcenEnxR9n1kd/D6dBLz9wwVjW6ss7xJ1ocOBXCLnXXIBumfgMrL/8oSGOZZGq+nvDAuJS9BX0dbKd/tEgwg52TYDgH8lnjWPa5uwmXNA47EssGHMOJhHHOWQ3ebKj3XGgf73BffG3Gw3hPQDHbUd7NDaPPG4O5WlIjolnDtJU/yKutzgnvNfEdZs3TMJmyqZ9DVhJdiGWxnDZhN+Vs2fIymUw==', 'APP_SECRET': 'icOZTPLQ8XRV6v50Dv/X/pTxvgkDzpOg9ZLZgzl2Fgl6WT7CZwewWyxVh11f42za2G9jeRZXBlN6jc4qq5sX3YUrcjosb1RMABJh72jAeo8sYJmwO9plsylDqLr9wXReYA9ULMlbi97nQtNK6xUQa4hA4geOhJhXExGde951j9KsCkC75WUYn6xzGmFMkzGCuk/QIXRkp75LeQTqntuB4+yMJ7otbmFt5rxJvdohbOopcHTvloNXpjkNWx9e6QBaqo8Ds1N9VjRfSfPsjdoY6p9tbqRCSqS3PhKNaOTDuv9AZr+vOAtX59UcD9H5P/bE46USn6Br7cC2ATTCl2+CC7/oxuv4OlZfFDEGPfyfBLpaGoKv+nZ23fkcxWNq2QHt/eoUng==', 'kms_domain': 'https://kms-msp.test.zte.com.cn', 'kms_uri': '/kms-ext/v1/application/decrypt_datakey', 'secretKeyType': 'apoll:test:product_key', 'secretKeyId': '1+TmwXFhWBu60xSJe0UzdkIjdRo+87S+n4mgqa+TFdsjc2n5KF0qF1+uuV9sK+gm7tVBCovPdIvUODpejQsSZvPKzvuf0M+fT7pfjXmPcksSzL4gVHg90tH3mLditXDzM8b9SQK+mFiElnJSXreSkAAzCZwPuj7hq9Rl+JfhxppFzOJgSJriZ1XQLu/DtMnWShYPfY9gefvUiSSgBycdKg2Jt4VBCmdqHOABI2jEfSly1Kj6H9ANv8LY5AXIvoOCeDY3hEA1znPkuP2HEthvPJV2oyqm77HelcH4Rpxutn0T2A1nKDudFnCNkEHBnHPRGQZBJXfi6dfmMNCVsOlCPlMJ2cekac2JKsjYiiLg26LS6al+ccuEG6zbWICBYVCcui5bUw==', 'dataKeyCipher': 'YzQyODY3MWViNWNiNGU1ZWQxZDdjZjM5N2FkOGZlMTA2NmNhOTNmMWZlZDRmYmYxMjJhNWU2NzRmZTk0ODE2ZTRmNzY5NTIwYzQ2MjBiNjM0YmRhZGY2NzkxZGNjOTBi$$ODJiYzA5YjNjYzkyNmY5MjQyMzQyOTBmMGJmOGYxMjE=', 'key-flag': 'b1HA9ddPIIo8W2v8YlwsynvZZijYrOdSUjy0Mn0GjyE=', 'Embedding': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector', 'name': 'Bge_M3'}, 'Milvus': {'collection_name': 'productQA_1118', 'MILVUS_HOST': '**************', 'MILVUS_PORT': 19530}, 'rerank': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-reranker/rerank', 'Reranker_tfidf': {'threshold_origin': 0.7, 'threshold_tfidf': 0.23, 'threshold_tfidf_binary': 0.23, 'threshold_origin_max': 0.98, 'threshold_origin_min': 0.1, 'threshold_tfidf_max': 0.65, 'threshold_tfidf_binary_max': 0.65, 'threshold_origin_advantage': 0.997}}, 'LLM': {'url': 'https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat', 'name': 'NebulaBiz', 'temperature': '0', 'top_p': 0.85, 'top_k': 5, 'llm_headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer 75a51e9169d0484aac31271a515c6e0a-e256ed047d71457b82a376996634ff17', 'X-Emp-No': '10345316'}, 'rewrite_model_name': 'intention-recognition', 'rewrite_model_type': 'openai', 'rewrite_model_url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-llmrewrite/v1/chat/completions'}, 'Relate': {'simwords_url': 'https://llm.dev.zte.com.cn/zte-dmt-dmp-judgechat/multy'}, 'catch_body': {'entity_es_index': 'kg_product_dn20240327'}, 'kg': {'space': 'product_multiversion', 'kg_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql'}, 'Knowledge': {'es_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/es/commonSearch', 'index': 'product_1206', 'index_xbk': 'product_1206', 'ner_index': 'product_infogo_entitylink', 'doc_file_info_index': 'product_doc_info_0912', 'user': 10265786, 'type1': ['content'], 'es_headers': {'Content-Type': 'application/json', 'User': '10265786', 'systemName': 'kgnlp'}, 'access_environment': 'prod', 'access_key': '5b33d571ae464910822a2bbf167e7f6e', 'access_secret': '36ba38e1d3f58f8b3c2d25d48a00b19b96f95e65dbe7f9b3f4ef921543578200'}, 'Parameters': {'address': '0.0.0.0', 'port': 10023}, 'Enviroment': 'dev', 'log': {'level': 'INFO', 'path': './resource/logs/mylog.log', 'dir': './resource/logs'}, 'kafka': {'broker': ['************:9092', '************:9092', '************:9092'], 'topic': 'acm-bk-app-nj'}, 'identify': {'intercept': ['/zte-ibo-acm-productretrieve/**'], 'enable': True, 'exclude': ['/zte-ibo-acm-productretrieve/info'], 'uac': {'secret': 'ER5LKw3L8q65zuLI0xcaHPtDMMTfDPALpatdMwvg4d4/tb5kGtEvokxS1+opacpXdg5s/G8mE9lBWKWK48iNeGogA6lU8XWUBfZ2C8sJ1s/wn0gNxi7cBUegyGfw0NmOBjyz5q/CocMycpof0IutH9mbZH9JRULUf5tGpkhD9uSkzim7+s0vLJm3y7wxnBpGBiD1sOXFB9ByAYXtWMshsvqXoOT4zYrwUNyIcaxjTfX0UoEfm/QjTW1NsFEJiNfZcuy5H0L8ETKs67GhL23VJ/t753vLGBVqS35BeKQvwbZfKKhmU27dqRHtBDfAg0foLawNHueyDFdRq6RwxzbeXQeGCX7DFx+kL3zfIa9nbZw=', 'secret-key': 'fpCehD17t2oVTS254PTQPK2k++H5xkfFANRQjK5/dg4xklvj1G5P5unW01r7qFCXtcG5bbcFqxibDnbHP221AqWISwZJ4clXM0JpiPZzzZrSYukMw/9cP5ClW0r+/lJRLjHLBLzKHaSA8Bp363JKuG+isL6U5DG4X1BzDd0fVF2tupECMzl+0/RxftmbKYDZz/qe/ARqFBYVOBkhGoRdGmNO0NtwwFLe3Bi1EMN5OFMsUoR5bVv9AvrqYz8aDwdmuWP+dGcI5p5umUFG6G7pxBQDk5eaircwwu0+tRlEIHIAYngDs9C5wIi9jQAx5TqHZBL0Ns3bTEhJNTNesWCcscqg0pS7bu6Sk63se3puwNr6Ffi4SyPepgkPpJldfXzJZyVioAp4o+9Lzw2hyrFPFndWwpwXtrernshitjDj4gc=', 'app-id': '612791211526', 'access-key': 'Tl1zN2Rn9xb0bSUOlNdTiNzASWfHlnkG0Gz4UqlbtbZQMbXb0Ssntr5f1vY4us4n3WOijf/FNuhWiREKlHAzLwKfc9XGhX3/hGmJj5o+5ZOmgyqAO3+y+jYTpyu754O0KFMJ3/DebucSRxLh9j90YVnGC7eXP2u9PuNoPvu8TIVSDoUBhoFcQWOU51vIF0Eb/CXoCfT7vBXex/DuCW30g6iMSXhfzyUZI545N1slVI7aKfv+78DCkLBbNzLaT9EVbwRyzg2+hzcfvJwH88s7yPx6O2DkfzlrHlac3vmJaSR2afAGIcNg+UcpKeTtOnGTiHUdF9eiBiCx8y/Z99wnPzNnZ9NCDpHJnspiI2wz0YoLMne/PkG10KTVuTwNosdt', 'verify-url': 'https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/external/auth/token/v2/verify.serv', 'itp-value': 'jFxDNBMtBVqGNVoOyh72vejSGsfClD2YsXF1OTvXcDZg9pX5fz+op3+Kl5qgOMsChwh44LsmRQTek+On1ytAG8KBGtNF/c/nwpJ8lJmeY1sP1jYqL8VBlgiTipOrfJezux1mQ81ufB2oYmS7tm5qHPAwqLsE1eGXKsMu4SuIcQEJqvizjk5xoq+wmB88qdfRv8jN/9WxJpSnPMIPKiuDohiFHIUwBPBzDWkkfSvmSAq0fRaG8na8dKZtWc7hMTlNgcqKRpZFCsW2f5Su8a4o2/RBy7FPHLHvyvcwZyTAUIXIhsrEPTBGy/2wjXZxVmhQ3EAr1JBZ5CS0cvTmRFlmO6HBKbPj4BvB31obPE5UzDyK/FiAPitUTLA1jARRNMiVabDI0oCoDHfERw==', 'encoding-utf8': 'utf-8', 'tenant-id': '10001', 'system-code': 'UAC', 'client-ip': '127.0.0.1'}}}

# 调试ok测试环境配置
config = {'MicroserviceName': '/zte-ibo-acm-productretrieve', 'BaseUrl': 'https://llm.test.zte.com.cn',
          'APP_NAME': 'XboIAdkoPGr6dhU6j0vFyr1E+2sAO5WDSsqofqMjHpnncQoGh2s7EtueLkNQFcA5MAP+xEYKWkJ5/LqNCT8+e5xwMFwIfd2+1uTSJmVYdPCJTWhCE4D2TtMwxKWAEgLmKhbpccuKICd3Mlzwd42P6+L7ATSoykF417GAMkXz7whRKhpzj+OY9fOb6Lh1eEJn0Lhuw3PSFcvHuOh7ys4bd/swF0OihCQd2dduUHaKytYHyBNpiDulsdznOI1vHgNEauZ4LHPuEVzQXE8M36rpTmrIlcq8P1GrkTVlDx9D9wQv/5620U3eSDs2QsDdt0IsdLObcbItTdRRpc8HGVuM8tykauSOUHPQ',
          'USER_ID': 'rT13EAE/KqhyYCXaT7M1UIGvRozZw44kixI4yeL52evg6i19Tc37M8unKlRLShUjM5HRZUeJfEfW6pasVoSzb7XBTOJ50b/qFCYkhMpu8W+jZWPH4PENDrmF/m9ahTbtC2wLpJ9FBytZAcenEnxR9n1kd/D6dBLz9wwVjW6ss7xJ1ocOBXCLnXXIBumfgMrL/8oSGOZZGq+nvDAuJS9BX0dbKd/tEgwg52TYDgH8lnjWPa5uwmXNA47EssGHMOJhHHOWQ3ebKj3XGgf73BffG3Gw3hPQDHbUd7NDaPPG4O5WlIjolnDtJU/yKutzgnvNfEdZs3TMJmyqZ9DVhJdiGWxnDZhN+Vs2fIymUw==',
          'APP_SECRET': 'icOZTPLQ8XRV6v50Dv/X/pTxvgkDzpOg9ZLZgzl2Fgl6WT7CZwewWyxVh11f42za2G9jeRZXBlN6jc4qq5sX3YUrcjosb1RMABJh72jAeo8sYJmwO9plsylDqLr9wXReYA9ULMlbi97nQtNK6xUQa4hA4geOhJhXExGde951j9KsCkC75WUYn6xzGmFMkzGCuk/QIXRkp75LeQTqntuB4+yMJ7otbmFt5rxJvdohbOopcHTvloNXpjkNWx9e6QBaqo8Ds1N9VjRfSfPsjdoY6p9tbqRCSqS3PhKNaOTDuv9AZr+vOAtX59UcD9H5P/bE46USn6Br7cC2ATTCl2+CC7/oxuv4OlZfFDEGPfyfBLpaGoKv+nZ23fkcxWNq2QHt/eoUng==',
          'kms_domain': 'https://kms-msp.test.zte.com.cn', 'kms_uri': '/kms-ext/v1/application/decrypt_datakey',
          'secretKeyType': 'apoll:test:product_key',
          'secretKeyId': '1+TmwXFhWBu60xSJe0UzdkIjdRo+87S+n4mgqa+TFdsjc2n5KF0qF1+uuV9sK+gm7tVBCovPdIvUODpejQsSZvPKzvuf0M+fT7pfjXmPcksSzL4gVHg90tH3mLditXDzM8b9SQK+mFiElnJSXreSkAAzCZwPuj7hq9Rl+JfhxppFzOJgSJriZ1XQLu/DtMnWShYPfY9gefvUiSSgBycdKg2Jt4VBCmdqHOABI2jEfSly1Kj6H9ANv8LY5AXIvoOCeDY3hEA1znPkuP2HEthvPJV2oyqm77HelcH4Rpxutn0T2A1nKDudFnCNkEHBnHPRGQZBJXfi6dfmMNCVsOlCPlMJ2cekac2JKsjYiiLg26LS6al+ccuEG6zbWICBYVCcui5bUw==',
          'dataKeyCipher': 'YzQyODY3MWViNWNiNGU1ZWQxZDdjZjM5N2FkOGZlMTA2NmNhOTNmMWZlZDRmYmYxMjJhNWU2NzRmZTk0ODE2ZTRmNzY5NTIwYzQ2MjBiNjM0YmRhZGY2NzkxZGNjOTBi$$ODJiYzA5YjNjYzkyNmY5MjQyMzQyOTBmMGJmOGYxMjE=',
          'key-flag': 'b1HA9ddPIIo8W2v8YlwsynvZZijYrOdSUjy0Mn0GjyE=',
          'Embedding': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector', 'name': 'Bge_M3'},
          'Milvus': {'collection_name': 'productQA_0810_mid', 'MILVUS_HOST': 'icrm.dev.zte.com.cn',
                     'MILVUS_PORT': 19530},
          'rerank': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-reranker/rerank',
                     'Reranker_tfidf': {'threshold_origin': 0.7, 'threshold_tfidf': 0.23,
                                        'threshold_tfidf_binary': 0.23, 'threshold_origin_max': 0.98,
                                        'threshold_origin_min': 0.1, 'threshold_tfidf_max': 0.65,
                                        'threshold_tfidf_binary_max': 0.65, 'threshold_origin_advantage': 0.997}},
          'LLM': {'url': 'https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat', 'name': 'DeepSeek-V3',
                  'temperature': '0', 'top_p': 0.85, 'top_k': 5, 'llm_headers': {'Content-Type': 'application/json',
                                                                                 'Authorization': 'Bearer 75a51e9169d0484aac31271a515c6e0a-e256ed047d71457b82a376996634ff17',
                                                                                 'X-Emp-No': '10345316'},
                  'rewrite_model_name': 'intention-recognition', 'rewrite_model_type': 'openai',
                  'rewrite_model_url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-llmrewrite/v1/chat/completions'},
          'Relate': {'simwords_url': 'https://llm.dev.zte.com.cn/zte-dmt-dmp-judgechat/multy'},
          'catch_body': {'entity_es_index': 'kg_product_dn20240327'}, 'kg': {'space': 'product_multiversion',
                                                                             'kg_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql'},
          'Knowledge': {
              'es_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/es/commonSearch',
              'index': 'product_1206', 'index_xbk': 'product_1206', 'ner_index': 'product_infogo_entitylink',
              'doc_file_info_index': 'product_doc_info_0912', 'user': 10265786, 'type1': ['content'],
              'es_headers': {'Content-Type': 'application/json', 'User': '10265786', 'systemName': 'kgnlp'},
              'access_environment': 'prod', 'access_key': '5b33d571ae464910822a2bbf167e7f6e',
              'access_secret': '36ba38e1d3f58f8b3c2d25d48a00b19b96f95e65dbe7f9b3f4ef921543578200'},
          'Parameters': {'address': '0.0.0.0', 'port': 7567}, 'Enviroment': 'dev',
          'log': {'level': 'INFO', 'path': './resource/logs/mylog.log', 'dir': './resource/logs'},
          'kafka': {'broker': ['************:9092', '************:9092', '************:9092'],
                    'topic': 'acm-bk-app-nj'},
          'identify': {'intercept': ['/zte-ibo-acm-productretrieve/**'], 'enable': True,
                       'exclude': ['/zte-ibo-acm-productretrieve/info'], 'uac': {
                  'secret': 'ER5LKw3L8q65zuLI0xcaHPtDMMTfDPALpatdMwvg4d4/tb5kGtEvokxS1+opacpXdg5s/G8mE9lBWKWK48iNeGogA6lU8XWUBfZ2C8sJ1s/wn0gNxi7cBUegyGfw0NmOBjyz5q/CocMycpof0IutH9mbZH9JRULUf5tGpkhD9uSkzim7+s0vLJm3y7wxnBpGBiD1sOXFB9ByAYXtWMshsvqXoOT4zYrwUNyIcaxjTfX0UoEfm/QjTW1NsFEJiNfZcuy5H0L8ETKs67GhL23VJ/t753vLGBVqS35BeKQvwbZfKKhmU27dqRHtBDfAg0foLawNHueyDFdRq6RwxzbeXQeGCX7DFx+kL3zfIa9nbZw=',
                  'secret-key': 'fpCehD17t2oVTS254PTQPK2k++H5xkfFANRQjK5/dg4xklvj1G5P5unW01r7qFCXtcG5bbcFqxibDnbHP221AqWISwZJ4clXM0JpiPZzzZrSYukMw/9cP5ClW0r+/lJRLjHLBLzKHaSA8Bp363JKuG+isL6U5DG4X1BzDd0fVF2tupECMzl+0/RxftmbKYDZz/qe/ARqFBYVOBkhGoRdGmNO0NtwwFLe3Bi1EMN5OFMsUoR5bVv9AvrqYz8aDwdmuWP+dGcI5p5umUFG6G7pxBQDk5eaircwwu0+tRlEIHIAYngDs9C5wIi9jQAx5TqHZBL0Ns3bTEhJNTNesWCcscqg0pS7bu6Sk63se3puwNr6Ffi4SyPepgkPpJldfXzJZyVioAp4o+9Lzw2hyrFPFndWwpwXtrernshitjDj4gc=',
                  'app-id': '612791211526',
                  'access-key': 'Tl1zN2Rn9xb0bSUOlNdTiNzASWfHlnkG0Gz4UqlbtbZQMbXb0Ssntr5f1vY4us4n3WOijf/FNuhWiREKlHAzLwKfc9XGhX3/hGmJj5o+5ZOmgyqAO3+y+jYTpyu754O0KFMJ3/DebucSRxLh9j90YVnGC7eXP2u9PuNoPvu8TIVSDoUBhoFcQWOU51vIF0Eb/CXoCfT7vBXex/DuCW30g6iMSXhfzyUZI545N1slVI7aKfv+78DCkLBbNzLaT9EVbwRyzg2+hzcfvJwH88s7yPx6O2DkfzlrHlac3vmJaSR2afAGIcNg+UcpKeTtOnGTiHUdF9eiBiCx8y/Z99wnPzNnZ9NCDpHJnspiI2wz0YoLMne/PkG10KTVuTwNosdt',
                  'verify-url': 'https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/external/auth/token/v2/verify.serv',
                  'itp-value': 'jFxDNBMtBVqGNVoOyh72vejSGsfClD2YsXF1OTvXcDZg9pX5fz+op3+Kl5qgOMsChwh44LsmRQTek+On1ytAG8KBGtNF/c/nwpJ8lJmeY1sP1jYqL8VBlgiTipOrfJezux1mQ81ufB2oYmS7tm5qHPAwqLsE1eGXKsMu4SuIcQEJqvizjk5xoq+wmB88qdfRv8jN/9WxJpSnPMIPKiuDohiFHIUwBPBzDWkkfSvmSAq0fRaG8na8dKZtWc7hMTlNgcqKRpZFCsW2f5Su8a4o2/RBy7FPHLHvyvcwZyTAUIXIhsrEPTBGy/2wjXZxVmhQ3EAr1JBZ5CS0cvTmRFlmO6HBKbPj4BvB31obPE5UzDyK/FiAPitUTLA1jARRNMiVabDI0oCoDHfERw==',
                  'encoding-utf8': 'utf-8', 'tenant-id': '10001', 'system-code': 'UAC', 'client-ip': '127.0.0.1'}}}

## prod 未测试通过
# config = {
#     'MicroserviceName': '/zte-ibo-acm-productretrieve',
#     'BaseUrl': 'https://llm.dt.zte.com.cn',
#     'APP_NAME': 'FmITS7RUB+0aOCFeTvIf9Q+cA9OXLahi56hoKo/vh2dgX+kyvQuZ6aXHiPuIN9m6/JIo0zmFc6mtOUd70g820OTWg2GXIBXu0Z6ooGL2hNN3vWYHKLGQLHbaqws222BBOe9xycjQtbhK5RLLhKI54uBuri53hqHnMYFSUgvNCUtT6Fg42XdETmo5EJw7Tb4C012JqVXvUE6kuDGv3iDnYNT8QMCXOOlxETmoOVfGfH1F/qEDqfZTmY4DdcN0w/hDBSiRW2BkrNa081bZk2SI5Bc7BiuAsDPMJSIuywYtUpienby40AdzfjhtkkU8u4gwWjY2ZDVT1c1xvMkHenDg5i5IVCwidUmnznSMPJM=',
#     'USER_ID': '4PCXDSy0tZlkS2zoeyT0vVlPCYYaV6n+Cyi4wADC7+JiypQkaXhmhLMpi5hRqJrhS1vXCaqF+q2nQ9m9bzUAtVBi16ScKcyrETXAMK8Si7rZQv+Yt8VKfEXrMMjYRfuO4fST4FlXaTr1KKtLIlxzpD6bTv3GpSueeH2wSi0ZamyJ9AxeY7qxMKA9qk7GAAJajZ5Nti9T0PzeDUeN67WsD8kbYYS3Ivl2mEshF0figuTsph++Gz1pH6sheOWskCfVCDB8AG8xt9fT+WVwz/Qr0nMetnRqcMz1PZnvuUafLuorcC0s8v1anRerEn55yHqwT54qvAP2Zb+cSxCBu+T+XfsPkcJy08QIAGJHLg==',
#     'APP_SECRET': 'Xbra1SaPv7R4vqOVfIKHvPX8V6J5VgbXT64ZHFceKkGyauCKaZpCotV8YcFDx6oNjBAcPXNgUn3ZCO7Ro8uKXtoUtcQIAdCFVOWD9jeC6cAeqF8v+QeSJ6or7Gdzy3v3mB5Nw2ZoBPSbtDF+MS4V+D8mEROd7ggOGdTki2AzCiTWRsY2qbYa3KgElKpatgQ39E65VE5Oe8PhG5KxwDg2cjrtCFkr84UbfBH+4okZSHrACvEOEGgf1lgCcAOn9Yc5IVKGuTAdz/LjxTQslN4CA+gOaY3oqYht/PyvO90GWVZVDxk3oFuRtU/Ft1dXFwOYrlFMBF8gIMOxrgxwVry8gqbUPN/SakyJD4b2Z8vNx8oPW1D8Ai31n90IjAS8H005lKuqjw==',
#     'kms_domain': 'https://kms.dt.zte.com.cn:8443',
#     'kms_uri': '/kms-ext/v1/application/decrypt_datakey',
#     'secretKeyType': 'apoll:prod:Apollo_key',
#     'secretKeyId': 'PUrgumVn98dFhpnHipi4E5AIrNb59m94uJo5rBJgoFPYGETb5b55kcTPm3Bp5fti7rbhgW2SlllLwv4lPamfz9lEBD0MjMFf/cubN/cFeC78hBVfT6SxsZlG5qIJe1lFyybJ4swiDnhFSP0iAe9C6kB4/seMcNGLTF4Puv1o/J7+hjyuH7D1KfEnv2NUHbwmQUUCrempaVdOqpiQLzrhK/fjpjGBOzJLLdz7y6c3uJ4zM/08EkfWkAo6AQquPjwRddpPyLDydhR1wv0ofQgIOi/EBiVNRy3vMSN6jMevMxhMSbi7qUn9RtAKBSvam+mRPRYWByJ4X9yNfUlUavczKNEb8Qd4GEWLe24ckEq9iLyxtMNjgGJADViXS60F/gYqoDymmQ==',
#     'dataKeyCipher': 'YThlZGY3ZDllZTAwNWY1NmM0N2I5YzA5OTI0NmE1OTE1MDk0YzQ2NDA2N2JjNjJmODFiNTdlMzVkYjA3MGI1NjBjM2VjYTYwZGQ1YTZhM2RlNzdjM2U2OGRiMmZkOTYw$$MGYwODMyZWQ1ZTI3NjMwMmM1OThkZDlkYTM4Y2Q0MmU=',
#     'key-flag': 'hIABNqNK0fo6mRFiHiZ5XV6030MAdtFPKwDY+VHba7BlN1fsoco7vMhge2js4lc2/CDBk0gCycB1Zyn8Bj5M1ithLmwYRsQdKqlX9eYMAI9luUhiRwkcSeC2Kg9WI4TAxqTAY/36YySnYDYYB49n8Y9bhIKKRT37m44txExckbtkqtSVQ1aCmzmhxmxQM12+IbxOg8A9M2D6rFRDeW8XsjuF3GJoxJhxYWC0wevPSs4OMXYh0HtwRB3kt7cdhWcDRqaQqi5JE2RlwM88aJwrg2k0EXslc5fYpZd1p3bok5J5p7bR72OHkQVRL/9EWbbYmt8HNnA/doiflHVfKKdAFh1QTzpdsBRA6Q4RKHXM1Ca8sXdQgz0zeR7VccSQ2thli4VhZ/2gmvPY/YN2',
#     'Embedding': {
#         'url': 'https://kger.zte.com.cn/zte-dmt-bge-vector-pro/bge-m3',
#         'name': 'Bge_M3'
#     },
#     'Milvus': {'collection_name': 'productQA_0810_mid', 'MILVUS_HOST': 'icrm.dev.zte.com.cn', 'MILVUS_PORT': 19530},
#     'rerank': {
#         'url': 'https://kger.zte.com.cn/zte-dmt-dmp-reranker/rerank',
#         'Reranker_tfidf': {
#             'threshold_origin': 0.7,
#             'threshold_tfidf': 0.23,
#             'threshold_tfidf_binary': 0.23,
#             'threshold_origin_max': 0.98,
#             'threshold_origin_min': 0.10,
#             'threshold_tfidf_max': 0.65,
#             'threshold_tfidf_binary_max': 0.65,
#             'threshold_origin_advantage': 0.997
#         }
#     },
#     'LLM': {
#         'url': 'https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat',
#         'name': 'DeepSeek-V3',
#         'temperature': '0',
#         'top_p': 0.85,
#         'top_k': 5,
#         'llm_headers': {
#             'Content-Type': 'application/json',
#             'Authorization': 'JmJnocV3RPOjNbwgwpoLDY2ZpHrrWlLCqPBHf6pxIl6Easpsi2MNFA92G4I3D/9Z5QEF+cOElPT5UgpK8d/T7Xp54hneAyuI9kF2/hfEh8uTSJ0x9mueal4wzIKr/8YIZyWEYdH4TqBGGIr9PrzWhwW5kHa3wJk+kjjZAc1lLziIUriKdwqtnNqM9JXLb3T+vUTA+wdSbcgWghUZ6evAMz240ZVmzh3dG9MPFAXc8v06SW5gqaeh16+oZG/clgc+wzXQlh12keWDCl9oRj5PvfdTRI02lBwidkLNdjRu0lguRfx+w0qtXlwA04SJM+0p6WZ6uOJ9Bdm7jzeGYoyEd0ytl9wgcR4gch03iMOceT6CesXoroDw7zqOsqo+Rj4zvbqzz3doAxxYVMkgoPRKQecVfjgwt3ehWts9NYIjQHLoJE4Djg0wmg==',
#             'X-Emp-No': '10345316',
#             'X-Auth-Value': '123'
#         },
#         'rewrite_model_name': 'intention-recognition',
#         'rewrite_model_type': 'openai',
#         'rewrite_model_url': 'https://llm.zte.com.cn/zte-igpt-ellm-llmrewrite/v1/chat/completions'
#     },
#     'Relate': {
#         'simwords_url': 'https://kger.zte.com.cn/zte-dmt-dmp-judge_chat/multy'
#     },
#     'catch_body': {
#         'entity_es_index': 'kg_product_dn20240327'
#     },
#     'kg': {
#         'space': 'product_multiversion',
#         'kg_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql'
#     },
#     'Knowledge': {
#         'es_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/es/commonSearch',
#         'index': 'product_1206',
#         'index_xbk': 'product_1206',
#         'ner_index': 'product_infogo_entitylink',
#         'doc_file_info_index': 'product_doc_info_0912',
#         'user': '10265786',
#         'type1': ['content'],
#         'es_headers': {
#             'Content-Type': 'application/json',
#             'User': '10265786',
#             'systemName': 'kgnlp'
#         },
#         'access_environment': 'prod',
#         'access_key': 'I79tQSaVknFszcIApRtOhQRyD2VZFYfrAhFP3+5jltOPrnW+VCaTROgv14B92kx84uM7ilyZR7qPXKatdWND/c3EvxQN+1OiIAXHXhkvXL/auvuKQcRKDfQlPqZwxYOIwf+JR7Di3pucoAcyJqs4OQaqAWbsAi1jxyPh6tc82gKdKL79YK4nRMCVy+Frs5RR52v6I7MwQZZHTYOTYF25X9sf4I18bNIG0yQRWn/eBITI88UNyT6yIL9lc+zQKhOvIRMmslyrgmwSJdMZemqrO+AGsAhRzrfH4OiOLem3isZDLZnUPExGCxwqF9zcO7atspJj4vPMkxh8HqVL4zDbzoQYXrkNohdFeS6ktf4hyvQP8J5T5Kc7A3KbHOJYA94k',
#         'access_secret': 'H3rSxJspdSycV9iNKFDd8PyLR1EfRSEheWTt1YyB5DiDmFoN/WyCUGkMp38Ba2K/z4y0rGRmIsL7M/86LYp1HFTPBJA2BZ9UJMCp3IiDvml/5ohM5PeUQJuFekuS1Kl1Q7O8buzka6wwspGvdt5UThuoXkejt5aS6QyiIiRU0QgsLdz1oLYMUhQoi1lw3aHgwvdHve7fKEp2SgcOYFVap4+sQK8ez2Ca8VCOhj0m5f1Sz7jPl0z4DMDY93h4TqF/5tAon2Kj41jqA2IB8FDcd1ys9qYO2w/GqFZQTSSObn+Y6gbwWnvwXABJFIUszLapIrU53nMfUjnKCLQ3K842xmZLjCnLW55FWNGU3dS8nlXPmgn5XXJwJ/XsosoUTrh60nD5bowi+99OnS+hB9HlTYbLWx2dI5FKanldBop+vS4='
#     },
#     'Parameters': {
#         'address': '0.0.0.0',
#         'port': 100
#     },
#     'Enviroment': 'prod',
#     'log': {
#         'level': 'INFO',
#         'path': './resource/logs/mylog.log',
#         'dir': './resource/logs'
#     },
#     'kafka': {
#         'broker': ['kafka-lcs-003.dt.zte.com.cn:9092'],
#         'topic': 'acm-bk-app-nj'
#     },
#     'identify': {
#         'intercept': ['/zte-ibo-acm-productretrieve/**'],
#         'enable': False,
#         'exclude': ['/zte-ibo-acm-productretrieve/info'],
#         'uac': {
#             'secret': 'oMwM+Yrxtq8ftpl3+7xHw75nNnQRucmNMWP0ixOEO6oI0tNTOKKpbf76ic7ZI6duzMpeVS3zywT4uwFdeE1MYKwr7WPw9OFazdKODXxoSeDr/AHpP8FX47jpg1lw0tsqUcvuO12j9BPxh3JoF1VlmUaPHjYu1J6B0kFuEb3k2nVPmfbgFvyB42eLl5osu7BcnFYDA3BydcHhsNJWS0que3ee8Y/Isw6tCIpxpBBc0KPGiHtR76SfWzfh/o5Y84H5U+XJWQrZHBTxUPGjil57c8qdjp3+6d1cxUOOYtjWgIytM+d7jOUSlotjz8hH2vSazWDXVkyJFCzPTx1D4hTWjPltYrcjjWZF5a2oqmI6nbQ=',
#             'secret-key': 'yS9LrPHCcMDs1fi/6XLUx21dF7ReLvhckpZIRYrFuVJE1bRv9S7j66Zj8BAp6MwID7zXCi48MTN6OD9q7JKMNdN6nWiFtOAqZ0So7icw30CUIP1m/WUCO26f05UnylkRRak0OiT9WLJwHzUkaSSARAOq7xe0PBUSoqJEC9dJKiYuLqt7jHDljml9zYqB7AEE19hQiHQYmBIClCeiRlgO1lN5UvBUMctNr9qGoVLTEQ8T9klFP4mF2LzOLYev4jrZyrawazDUHd5kDCSK4X5vX9BrVx2Rtlo8NkQhsfjGiTk+FM8OdT3S4nOqOoBZyAQE5Y2CKmJFGA3G4/2+eBtHuVka4ETOxckUolTSy0DxxFWqi5Zif8EqJjn8QH1BmIxIsiTS5Bur6W1zs//5au62JTZd0RwaZ68+24kDpp7b5NQ=',
#             'app-id': '248202511840',
#             'access-key': 'BrpAE/vk6mSNGYkyAoEMGBEyCUEWgdnWkh0O8BwHpZuXX8sZ2O6p/Nw9E4uI74lI5Wc92qsWb6gE5inqxi7xze7aJYJjB3K8Ln5xfXLJ4HOhOP6c/oP7uThkGcWEjtGGFLAGiE7l/kEpqZwg+nvw4KxFsIldTgGl7ooVNDgs85QYQamTypG8Ifz/l2ngYglTQUJzXYhXDfi8+4VGew6m+DgEuAmLVo2vlpxfIOIszrxm/zAWg1+QldJUQaH7w4GMcDqPTEagm223y+cVtnf9Zl7rwTnglMnqb4bkg5fg6JZkOF4tIe75MeQb+Xw3JSOKXEfa4Ydorv5tv7BfzJL37Bl4vmvJYgWGFb3V8xuC/GVC3oKHB2QV4DopzMJ6n7KV',
#             'verify-url': 'https://uac.zte.com.cn/zte-sec-uac-iportalbff/external/auth/token/v2/verify.serv',
#             'itp-value': 'q3AruwORrDG3PElliBaxjB17xLU5RVwYlSn5g88qWNou8/gNDa3iXVLOf2OusdcsEctQxvR+u6NXV7y0j3wvaFnrm9p4q5HYzW6I7RoVAhs/ahDveAGt29cqu8o5GoBHlMulWz8sOQONheUU1SggWcwkUT/URzqMmmF4IhPPvmKZeMu0pec1tFzY/vSBJpvi8ZLQ3dO2KUM5l6VRPwbuo0qXjxfPoUao2tES97OoZveQUFg5ETn8IvxclTGgI3tJpwmcebn1qn+UN5EqPT7LwRDDFPCb9bA2wPVjCmUQr+Cu6kRAd5IKfo2rApwaufwiVM2qgtgMsf8m8SlAeE8V/nh57jtolYPyGNPFmmJPFSnqgNaAWL+qr0XKREf8h8TpWFjMRb58/DDZWw==',
#             'encoding-utf8': 'utf-8',
#             'tenant-id': '10001',
#             'system-code': 'UAC',
#             'client-ip': '127.0.0.1'
#         }
#     }
# }
