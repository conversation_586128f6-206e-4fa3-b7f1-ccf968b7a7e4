import sys
sys.path.append("..")
from utils.logger.logger_util import logger
from utils.milvus_util.milvus_helpers import MilvusHelper
from pymilvus import Collection

class RecallMilvus(object):
    def __init__(self,config):
        self.collection_name = config['Milvus']['collection_name']

    def query_data_milvus(self,query,docname_list):
        
        milvushelper = MilvusHelper()
        collection = Collection(self.collection_name)
        if docname_list:
            expr_str = "doc_name in " + str(docname_list)

            search_param = {
                "data":query,
                "anns_field":"embedding",
                "param":{"metric_type": "COSINE", "params": {"nprobe": 1200}},
                "limit":20,
                "output_fields":["doc_name","document"],
                "expr": expr_str
            }
        else:
            search_param = {
                "data":query,
                "anns_field":"embedding",
                "param":{"metric_type": "COSINE", "params": {"nprobe": 1200}},
                "limit":20,
                "output_fields":["doc_name","document"],
            }

        res = collection.search(**search_param)
        res_name,res_doc,res_distance,res_id = zip(*[(i.entity.doc_name,i.entity.document,i.distance,i.id) for i in res[0]])
        res_name = list(res_name)
        res_doc = list(res_doc)
        res_distance = list(res_distance)
        res_id = list(res_id)

        key_list = ["doc_name","content","score","id"]
        result = [{key_list[0]:res_name[i],key_list[1]:res_doc[i],key_list[2]:res_distance[i],key_list[3]:res_id[i]} for i in range(len(res_id))]
        return result
